// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppSettings _$AppSettingsFromJson(Map<String, dynamic> json) => AppSettings(
  themeMode: $enumDecode(_$ThemeModeEnumMap, json['themeMode']),
  fontSize: (json['fontSize'] as num).toDouble(),
  fontFamily: json['fontFamily'] as String,
  lineHeight: (json['lineHeight'] as num).toDouble(),
  paragraphSpacing: (json['paragraphSpacing'] as num).toDouble(),
  editorSettings: EditorSettings.fromJson(
    json['editorSettings'] as Map<String, dynamic>,
  ),
  autoSaveSettings: AutoSaveSettings.fromJson(
    json['autoSaveSettings'] as Map<String, dynamic>,
  ),
  backupSettings: BackupSettings.fromJson(
    json['backupSettings'] as Map<String, dynamic>,
  ),
  statisticsSettings: StatisticsSettings.fromJson(
    json['statisticsSettings'] as Map<String, dynamic>,
  ),
  notificationSettings: NotificationSettings.fromJson(
    json['notificationSettings'] as Map<String, dynamic>,
  ),
  language: json['language'] as String,
  debugMode: json['debugMode'] as bool,
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$AppSettingsToJson(AppSettings instance) =>
    <String, dynamic>{
      'themeMode': _$ThemeModeEnumMap[instance.themeMode]!,
      'fontSize': instance.fontSize,
      'fontFamily': instance.fontFamily,
      'lineHeight': instance.lineHeight,
      'paragraphSpacing': instance.paragraphSpacing,
      'editorSettings': instance.editorSettings,
      'autoSaveSettings': instance.autoSaveSettings,
      'backupSettings': instance.backupSettings,
      'statisticsSettings': instance.statisticsSettings,
      'notificationSettings': instance.notificationSettings,
      'language': instance.language,
      'debugMode': instance.debugMode,
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$ThemeModeEnumMap = {
  ThemeMode.system: 'system',
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
};

EditorSettings _$EditorSettingsFromJson(Map<String, dynamic> json) =>
    EditorSettings(
      showLineNumbers: json['showLineNumbers'] as bool,
      wordWrap: json['wordWrap'] as bool,
      spellCheck: json['spellCheck'] as bool,
      syntaxHighlight: json['syntaxHighlight'] as bool,
      indentSize: (json['indentSize'] as num).toInt(),
      useSpacesForIndent: json['useSpacesForIndent'] as bool,
      editorTheme: json['editorTheme'] as String,
      focusMode: json['focusMode'] as bool,
      typewriterMode: json['typewriterMode'] as bool,
    );

Map<String, dynamic> _$EditorSettingsToJson(EditorSettings instance) =>
    <String, dynamic>{
      'showLineNumbers': instance.showLineNumbers,
      'wordWrap': instance.wordWrap,
      'spellCheck': instance.spellCheck,
      'syntaxHighlight': instance.syntaxHighlight,
      'indentSize': instance.indentSize,
      'useSpacesForIndent': instance.useSpacesForIndent,
      'editorTheme': instance.editorTheme,
      'focusMode': instance.focusMode,
      'typewriterMode': instance.typewriterMode,
    };

AutoSaveSettings _$AutoSaveSettingsFromJson(Map<String, dynamic> json) =>
    AutoSaveSettings(
      enabled: json['enabled'] as bool,
      intervalSeconds: (json['intervalSeconds'] as num).toInt(),
      saveOnFocusLoss: json['saveOnFocusLoss'] as bool,
      saveOnExit: json['saveOnExit'] as bool,
    );

Map<String, dynamic> _$AutoSaveSettingsToJson(AutoSaveSettings instance) =>
    <String, dynamic>{
      'enabled': instance.enabled,
      'intervalSeconds': instance.intervalSeconds,
      'saveOnFocusLoss': instance.saveOnFocusLoss,
      'saveOnExit': instance.saveOnExit,
    };

BackupSettings _$BackupSettingsFromJson(Map<String, dynamic> json) =>
    BackupSettings(
      autoBackupEnabled: json['autoBackupEnabled'] as bool,
      backupIntervalHours: (json['backupIntervalHours'] as num).toInt(),
      maxBackupCount: (json['maxBackupCount'] as num).toInt(),
      backupPath: json['backupPath'] as String,
      compressBackup: json['compressBackup'] as bool,
    );

Map<String, dynamic> _$BackupSettingsToJson(BackupSettings instance) =>
    <String, dynamic>{
      'autoBackupEnabled': instance.autoBackupEnabled,
      'backupIntervalHours': instance.backupIntervalHours,
      'maxBackupCount': instance.maxBackupCount,
      'backupPath': instance.backupPath,
      'compressBackup': instance.compressBackup,
    };

StatisticsSettings _$StatisticsSettingsFromJson(Map<String, dynamic> json) =>
    StatisticsSettings(
      enabled: json['enabled'] as bool,
      dailyWordGoal: (json['dailyWordGoal'] as num).toInt(),
      showRealtimeStats: json['showRealtimeStats'] as bool,
      trackWritingTime: json['trackWritingTime'] as bool,
      resetHour: (json['resetHour'] as num).toInt(),
    );

Map<String, dynamic> _$StatisticsSettingsToJson(StatisticsSettings instance) =>
    <String, dynamic>{
      'enabled': instance.enabled,
      'dailyWordGoal': instance.dailyWordGoal,
      'showRealtimeStats': instance.showRealtimeStats,
      'trackWritingTime': instance.trackWritingTime,
      'resetHour': instance.resetHour,
    };

NotificationSettings _$NotificationSettingsFromJson(
  Map<String, dynamic> json,
) => NotificationSettings(
  enabled: json['enabled'] as bool,
  writingReminder: json['writingReminder'] as bool,
  reminderTimes: _timeOfDayListFromJson(json['reminderTimes'] as List),
  goalCompletionNotification: json['goalCompletionNotification'] as bool,
  backupCompletionNotification: json['backupCompletionNotification'] as bool,
);

Map<String, dynamic> _$NotificationSettingsToJson(
  NotificationSettings instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'writingReminder': instance.writingReminder,
  'reminderTimes': _timeOfDayListToJson(instance.reminderTimes),
  'goalCompletionNotification': instance.goalCompletionNotification,
  'backupCompletionNotification': instance.backupCompletionNotification,
};
