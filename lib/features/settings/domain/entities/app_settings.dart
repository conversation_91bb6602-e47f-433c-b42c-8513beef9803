import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'app_settings.g.dart';

/// 应用设置实体类
@JsonSerializable()
class AppSettings {
  /// 主题模式
  final ThemeMode themeMode;

  /// 字体大小
  final double fontSize;

  /// 字体家族
  final String fontFamily;

  /// 行间距
  final double lineHeight;

  /// 段落间距
  final double paragraphSpacing;

  /// 编辑器设置
  final EditorSettings editorSettings;

  /// 自动保存设置
  final AutoSaveSettings autoSaveSettings;

  /// 备份设置
  final BackupSettings backupSettings;

  /// 统计设置
  final StatisticsSettings statisticsSettings;

  /// 通知设置
  final NotificationSettings notificationSettings;

  /// 语言设置
  final String language;

  /// 是否启用调试模式
  final bool debugMode;

  /// 最后修改时间
  final DateTime updatedAt;

  const AppSettings({
    required this.themeMode,
    required this.fontSize,
    required this.fontFamily,
    required this.lineHeight,
    required this.paragraphSpacing,
    required this.editorSettings,
    required this.autoSaveSettings,
    required this.backupSettings,
    required this.statisticsSettings,
    required this.notificationSettings,
    required this.language,
    required this.debugMode,
    required this.updatedAt,
  });

  /// 默认设置
  factory AppSettings.defaultSettings() {
    return AppSettings(
      themeMode: ThemeMode.system,
      fontSize: 16.0,
      fontFamily: 'System',
      lineHeight: 1.5,
      paragraphSpacing: 16.0,
      editorSettings: EditorSettings.defaultSettings(),
      autoSaveSettings: AutoSaveSettings.defaultSettings(),
      backupSettings: BackupSettings.defaultSettings(),
      statisticsSettings: StatisticsSettings.defaultSettings(),
      notificationSettings: NotificationSettings.defaultSettings(),
      language: 'zh_CN',
      debugMode: false,
      updatedAt: DateTime.now(),
    );
  }

  /// 从JSON创建AppSettings实例
  factory AppSettings.fromJson(Map<String, dynamic> json) =>
      _$AppSettingsFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$AppSettingsToJson(this);

  /// 复制并修改部分属性
  AppSettings copyWith({
    ThemeMode? themeMode,
    double? fontSize,
    String? fontFamily,
    double? lineHeight,
    double? paragraphSpacing,
    EditorSettings? editorSettings,
    AutoSaveSettings? autoSaveSettings,
    BackupSettings? backupSettings,
    StatisticsSettings? statisticsSettings,
    NotificationSettings? notificationSettings,
    String? language,
    bool? debugMode,
    DateTime? updatedAt,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      lineHeight: lineHeight ?? this.lineHeight,
      paragraphSpacing: paragraphSpacing ?? this.paragraphSpacing,
      editorSettings: editorSettings ?? this.editorSettings,
      autoSaveSettings: autoSaveSettings ?? this.autoSaveSettings,
      backupSettings: backupSettings ?? this.backupSettings,
      statisticsSettings: statisticsSettings ?? this.statisticsSettings,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      language: language ?? this.language,
      debugMode: debugMode ?? this.debugMode,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// 编辑器设置
@JsonSerializable()
class EditorSettings {
  /// 是否显示行号
  final bool showLineNumbers;

  /// 是否启用自动换行
  final bool wordWrap;

  /// 是否启用拼写检查
  final bool spellCheck;

  /// 是否启用语法高亮
  final bool syntaxHighlight;

  /// 缩进大小
  final int indentSize;

  /// 是否使用空格缩进
  final bool useSpacesForIndent;

  /// 编辑器主题
  final String editorTheme;

  /// 是否启用专注模式
  final bool focusMode;

  /// 是否启用打字机模式
  final bool typewriterMode;

  const EditorSettings({
    required this.showLineNumbers,
    required this.wordWrap,
    required this.spellCheck,
    required this.syntaxHighlight,
    required this.indentSize,
    required this.useSpacesForIndent,
    required this.editorTheme,
    required this.focusMode,
    required this.typewriterMode,
  });

  factory EditorSettings.defaultSettings() {
    return const EditorSettings(
      showLineNumbers: false,
      wordWrap: true,
      spellCheck: true,
      syntaxHighlight: false,
      indentSize: 2,
      useSpacesForIndent: true,
      editorTheme: 'default',
      focusMode: false,
      typewriterMode: false,
    );
  }

  factory EditorSettings.fromJson(Map<String, dynamic> json) =>
      _$EditorSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$EditorSettingsToJson(this);
}

/// 自动保存设置
@JsonSerializable()
class AutoSaveSettings {
  /// 是否启用自动保存
  final bool enabled;

  /// 自动保存间隔（秒）
  final int intervalSeconds;

  /// 是否在失去焦点时保存
  final bool saveOnFocusLoss;

  /// 是否在应用退出时保存
  final bool saveOnExit;

  const AutoSaveSettings({
    required this.enabled,
    required this.intervalSeconds,
    required this.saveOnFocusLoss,
    required this.saveOnExit,
  });

  factory AutoSaveSettings.defaultSettings() {
    return const AutoSaveSettings(
      enabled: true,
      intervalSeconds: 30,
      saveOnFocusLoss: true,
      saveOnExit: true,
    );
  }

  factory AutoSaveSettings.fromJson(Map<String, dynamic> json) =>
      _$AutoSaveSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$AutoSaveSettingsToJson(this);
}

/// 备份设置
@JsonSerializable()
class BackupSettings {
  /// 是否启用自动备份
  final bool autoBackupEnabled;

  /// 备份间隔（小时）
  final int backupIntervalHours;

  /// 最大备份数量
  final int maxBackupCount;

  /// 备份路径
  final String backupPath;

  /// 是否压缩备份
  final bool compressBackup;

  const BackupSettings({
    required this.autoBackupEnabled,
    required this.backupIntervalHours,
    required this.maxBackupCount,
    required this.backupPath,
    required this.compressBackup,
  });

  factory BackupSettings.defaultSettings() {
    return const BackupSettings(
      autoBackupEnabled: true,
      backupIntervalHours: 24,
      maxBackupCount: 7,
      backupPath: 'backup',
      compressBackup: true,
    );
  }

  factory BackupSettings.fromJson(Map<String, dynamic> json) =>
      _$BackupSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$BackupSettingsToJson(this);
}

/// 统计设置
@JsonSerializable()
class StatisticsSettings {
  /// 是否启用写作统计
  final bool enabled;

  /// 每日写作目标（字数）
  final int dailyWordGoal;

  /// 是否显示实时字数统计
  final bool showRealtimeStats;

  /// 是否记录写作时间
  final bool trackWritingTime;

  /// 统计重置时间（小时，24小时制）
  final int resetHour;

  const StatisticsSettings({
    required this.enabled,
    required this.dailyWordGoal,
    required this.showRealtimeStats,
    required this.trackWritingTime,
    required this.resetHour,
  });

  factory StatisticsSettings.defaultSettings() {
    return const StatisticsSettings(
      enabled: true,
      dailyWordGoal: 1000,
      showRealtimeStats: true,
      trackWritingTime: true,
      resetHour: 0,
    );
  }

  factory StatisticsSettings.fromJson(Map<String, dynamic> json) =>
      _$StatisticsSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$StatisticsSettingsToJson(this);
}

/// 通知设置
@JsonSerializable()
class NotificationSettings {
  /// 是否启用通知
  final bool enabled;

  /// 是否启用写作提醒
  final bool writingReminder;

  /// 写作提醒时间
  @JsonKey(fromJson: _timeOfDayListFromJson, toJson: _timeOfDayListToJson)
  final List<TimeOfDay> reminderTimes;

  /// 是否启用目标完成通知
  final bool goalCompletionNotification;

  /// 是否启用备份完成通知
  final bool backupCompletionNotification;

  const NotificationSettings({
    required this.enabled,
    required this.writingReminder,
    required this.reminderTimes,
    required this.goalCompletionNotification,
    required this.backupCompletionNotification,
  });

  factory NotificationSettings.defaultSettings() {
    return const NotificationSettings(
      enabled: true,
      writingReminder: false,
      reminderTimes: [],
      goalCompletionNotification: true,
      backupCompletionNotification: false,
    );
  }

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSettingsToJson(this);
}

/// TimeOfDay序列化辅助函数
List<TimeOfDay> _timeOfDayListFromJson(List<dynamic> json) {
  return json.map((item) {
    final map = item as Map<String, dynamic>;
    return TimeOfDay(hour: map['hour'] as int, minute: map['minute'] as int);
  }).toList();
}

List<Map<String, dynamic>> _timeOfDayListToJson(List<TimeOfDay> times) {
  return times
      .map((time) => {'hour': time.hour, 'minute': time.minute})
      .toList();
}
