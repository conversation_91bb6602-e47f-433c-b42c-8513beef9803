import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/entities/app_settings.dart';
import '../../../../core/constants/app_constants.dart';

/// 设置状态管理
class SettingsProvider extends ChangeNotifier {
  AppSettings _settings = AppSettings.defaultSettings();
  bool _isLoading = false;
  String? _error;

  // Getters
  AppSettings get settings => _settings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // 快捷访问器
  ThemeMode get themeMode => _settings.themeMode;
  double get fontSize => _settings.fontSize;
  String get fontFamily => _settings.fontFamily;
  double get lineHeight => _settings.lineHeight;
  double get paragraphSpacing => _settings.paragraphSpacing;
  EditorSettings get editorSettings => _settings.editorSettings;
  AutoSaveSettings get autoSaveSettings => _settings.autoSaveSettings;
  BackupSettings get backupSettings => _settings.backupSettings;
  StatisticsSettings get statisticsSettings => _settings.statisticsSettings;
  NotificationSettings get notificationSettings =>
      _settings.notificationSettings;
  String get language => _settings.language;
  bool get debugMode => _settings.debugMode;

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// 初始化设置
  Future<void> initialize() async {
    try {
      _setLoading(true);
      _setError(null);
      await _loadSettings();
    } catch (e) {
      _setError('初始化设置失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 加载设置（别名方法）
  Future<void> loadSettings() async {
    await initialize();
  }

  /// 从本地存储加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // 加载主题模式
    final themeModeIndex = prefs.getInt(AppConstants.themeKey);
    final themeMode = themeModeIndex != null
        ? ThemeMode.values[themeModeIndex]
        : ThemeMode.system;

    // 加载字体大小
    final fontSize =
        prefs.getDouble(AppConstants.fontSizeKey) ??
        AppConstants.defaultFontSize;

    // 加载其他设置...
    _settings = _settings.copyWith(
      themeMode: themeMode,
      fontSize: fontSize,
      updatedAt: DateTime.now(),
    );

    notifyListeners();
  }

  /// 保存设置到本地存储
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setInt(AppConstants.themeKey, _settings.themeMode.index);
    await prefs.setDouble(AppConstants.fontSizeKey, _settings.fontSize);
    // 保存其他设置...
  }

  /// 更新主题模式
  Future<void> updateThemeMode(ThemeMode themeMode) async {
    try {
      _settings = _settings.copyWith(
        themeMode: themeMode,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新主题模式失败: $e');
    }
  }

  /// 更新字体大小
  Future<void> updateFontSize(double fontSize) async {
    try {
      final clampedSize = fontSize.clamp(
        AppConstants.minFontSize,
        AppConstants.maxFontSize,
      );

      _settings = _settings.copyWith(
        fontSize: clampedSize,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新字体大小失败: $e');
    }
  }

  /// 更新字体家族
  Future<void> updateFontFamily(String fontFamily) async {
    try {
      _settings = _settings.copyWith(
        fontFamily: fontFamily,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新字体家族失败: $e');
    }
  }

  /// 更新行间距
  Future<void> updateLineHeight(double lineHeight) async {
    try {
      _settings = _settings.copyWith(
        lineHeight: lineHeight,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新行间距失败: $e');
    }
  }

  /// 更新段落间距
  Future<void> updateParagraphSpacing(double paragraphSpacing) async {
    try {
      _settings = _settings.copyWith(
        paragraphSpacing: paragraphSpacing,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新段落间距失败: $e');
    }
  }

  /// 更新编辑器设置
  Future<void> updateEditorSettings(EditorSettings editorSettings) async {
    try {
      _settings = _settings.copyWith(
        editorSettings: editorSettings,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新编辑器设置失败: $e');
    }
  }

  /// 更新自动保存设置
  Future<void> updateAutoSaveSettings(AutoSaveSettings autoSaveSettings) async {
    try {
      _settings = _settings.copyWith(
        autoSaveSettings: autoSaveSettings,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新自动保存设置失败: $e');
    }
  }

  /// 更新备份设置
  Future<void> updateBackupSettings(BackupSettings backupSettings) async {
    try {
      _settings = _settings.copyWith(
        backupSettings: backupSettings,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新备份设置失败: $e');
    }
  }

  /// 更新统计设置
  Future<void> updateStatisticsSettings(
    StatisticsSettings statisticsSettings,
  ) async {
    try {
      _settings = _settings.copyWith(
        statisticsSettings: statisticsSettings,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新统计设置失败: $e');
    }
  }

  /// 更新通知设置
  Future<void> updateNotificationSettings(
    NotificationSettings notificationSettings,
  ) async {
    try {
      _settings = _settings.copyWith(
        notificationSettings: notificationSettings,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新通知设置失败: $e');
    }
  }

  /// 更新语言设置
  Future<void> updateLanguage(String language) async {
    try {
      _settings = _settings.copyWith(
        language: language,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('更新语言设置失败: $e');
    }
  }

  /// 切换调试模式
  Future<void> toggleDebugMode() async {
    try {
      _settings = _settings.copyWith(
        debugMode: !_settings.debugMode,
        updatedAt: DateTime.now(),
      );
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('切换调试模式失败: $e');
    }
  }

  /// 重置为默认设置
  Future<void> resetToDefaults() async {
    try {
      _setLoading(true);
      _setError(null);

      _settings = AppSettings.defaultSettings();
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('重置设置失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 导出设置
  Map<String, dynamic> exportSettings() {
    return _settings.toJson();
  }

  /// 导入设置
  Future<void> importSettings(Map<String, dynamic> settingsJson) async {
    try {
      _setLoading(true);
      _setError(null);

      _settings = AppSettings.fromJson(settingsJson);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('导入设置失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 获取当前主题数据
  ThemeData getThemeData(BuildContext context) {
    final brightness = _getCurrentBrightness(context);
    // 这里可以根据设置自定义主题
    return ThemeData(
      brightness: brightness,
      fontFamily: _settings.fontFamily == 'System'
          ? null
          : _settings.fontFamily,
      // 其他主题配置...
    );
  }

  /// 获取当前亮度
  Brightness _getCurrentBrightness(BuildContext context) {
    switch (_settings.themeMode) {
      case ThemeMode.light:
        return Brightness.light;
      case ThemeMode.dark:
        return Brightness.dark;
      case ThemeMode.system:
        return MediaQuery.of(context).platformBrightness;
    }
  }

  /// 获取文本样式
  TextStyle getTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return TextStyle(
      fontSize: fontSize ?? _settings.fontSize,
      fontFamily: _settings.fontFamily == 'System'
          ? null
          : _settings.fontFamily,
      height: _settings.lineHeight,
      fontWeight: fontWeight,
      color: color,
    );
  }
}
