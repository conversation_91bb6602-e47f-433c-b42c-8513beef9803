import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/widgets/theme_switcher.dart';
import '../providers/settings_provider.dart';
import '../../domain/entities/app_settings.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_item.dart';

/// 设置页面
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('设置')),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(context),
        tablet: _buildTabletLayout(context),
        desktop: _buildDesktopLayout(context),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: _buildSettingsContent(context),
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: _buildSettingsContent(context),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: _buildSettingsContent(context),
        ),
      ),
    );
  }

  Widget _buildSettingsContent(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 外观设置
            SettingsSection(
              title: '外观',
              children: [
                SettingsItem(
                  icon: Icons.palette,
                  title: '主题模式',
                  subtitle: _getThemeModeText(provider.themeMode),
                  trailing: const ThemeSwitcher(),
                ),
                SettingsItem(
                  icon: Icons.text_fields,
                  title: '字体大小',
                  subtitle: _getFontSizeText(provider.fontSize),
                  trailing: DropdownButton<double>(
                    value: provider.fontSize,
                    underline: Container(),
                    items: const [
                      DropdownMenuItem(value: 12.0, child: Text('小')),
                      DropdownMenuItem(value: 14.0, child: Text('中')),
                      DropdownMenuItem(value: 16.0, child: Text('大')),
                      DropdownMenuItem(value: 18.0, child: Text('特大')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        provider.updateFontSize(value);
                      }
                    },
                  ),
                ),
                SettingsItem(
                  icon: Icons.language,
                  title: '语言',
                  subtitle: '简体中文',
                  onTap: () {
                    // TODO: 实现语言切换
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(const SnackBar(content: Text('语言切换功能开发中')));
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 编辑器设置
            SettingsSection(
              title: '编辑器',
              children: [
                SettingsItem(
                  icon: Icons.auto_awesome,
                  title: '自动保存',
                  subtitle: provider.autoSaveSettings.enabled ? '已开启' : '已关闭',
                  trailing: Switch(
                    value: provider.autoSaveSettings.enabled,
                    onChanged: (value) {
                      final newSettings = AutoSaveSettings(
                        enabled: value,
                        intervalSeconds:
                            provider.autoSaveSettings.intervalSeconds,
                        saveOnFocusLoss:
                            provider.autoSaveSettings.saveOnFocusLoss,
                        saveOnExit: provider.autoSaveSettings.saveOnExit,
                      );
                      provider.updateAutoSaveSettings(newSettings);
                    },
                  ),
                ),
                SettingsItem(
                  icon: Icons.timer,
                  title: '自动保存间隔',
                  subtitle: '${provider.autoSaveSettings.intervalSeconds}秒',
                  trailing: DropdownButton<int>(
                    value: provider.autoSaveSettings.intervalSeconds,
                    underline: Container(),
                    items: const [
                      DropdownMenuItem(value: 30, child: Text('30秒')),
                      DropdownMenuItem(value: 60, child: Text('1分钟')),
                      DropdownMenuItem(value: 300, child: Text('5分钟')),
                      DropdownMenuItem(value: 600, child: Text('10分钟')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        final newSettings = AutoSaveSettings(
                          enabled: provider.autoSaveSettings.enabled,
                          intervalSeconds: value,
                          saveOnFocusLoss:
                              provider.autoSaveSettings.saveOnFocusLoss,
                          saveOnExit: provider.autoSaveSettings.saveOnExit,
                        );
                        provider.updateAutoSaveSettings(newSettings);
                      }
                    },
                  ),
                ),
                SettingsItem(
                  icon: Icons.wrap_text,
                  title: '自动换行',
                  subtitle: provider.editorSettings.wordWrap ? '已开启' : '已关闭',
                  trailing: Switch(
                    value: provider.editorSettings.wordWrap,
                    onChanged: (value) {
                      final newSettings = EditorSettings(
                        showLineNumbers:
                            provider.editorSettings.showLineNumbers,
                        wordWrap: value,
                        spellCheck: provider.editorSettings.spellCheck,
                        syntaxHighlight:
                            provider.editorSettings.syntaxHighlight,
                        indentSize: provider.editorSettings.indentSize,
                        useSpacesForIndent:
                            provider.editorSettings.useSpacesForIndent,
                        editorTheme: provider.editorSettings.editorTheme,
                        focusMode: provider.editorSettings.focusMode,
                        typewriterMode: provider.editorSettings.typewriterMode,
                      );
                      provider.updateEditorSettings(newSettings);
                    },
                  ),
                ),
                SettingsItem(
                  icon: Icons.format_line_spacing,
                  title: '行间距',
                  subtitle: _getLineHeightText(provider.lineHeight),
                  trailing: DropdownButton<double>(
                    value: provider.lineHeight,
                    underline: Container(),
                    items: const [
                      DropdownMenuItem(value: 1.2, child: Text('紧密')),
                      DropdownMenuItem(value: 1.5, child: Text('标准')),
                      DropdownMenuItem(value: 1.8, child: Text('宽松')),
                      DropdownMenuItem(value: 2.0, child: Text('很宽松')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        provider.updateLineHeight(value);
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 数据管理
            SettingsSection(
              title: '数据管理',
              children: [
                SettingsItem(
                  icon: Icons.backup,
                  title: '备份数据',
                  subtitle: '导出所有数据到文件',
                  onTap: () => _showBackupDialog(context),
                ),
                SettingsItem(
                  icon: Icons.restore,
                  title: '恢复数据',
                  subtitle: '从备份文件恢复数据',
                  onTap: () => _showRestoreDialog(context),
                ),
                SettingsItem(
                  icon: Icons.delete_forever,
                  title: '清除所有数据',
                  subtitle: '删除所有本地数据（不可恢复）',
                  onTap: () => _showClearDataDialog(context),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 关于
            SettingsSection(
              title: '关于',
              children: [
                SettingsItem(
                  icon: Icons.info,
                  title: '版本信息',
                  subtitle: 'NovelICE v1.0.0',
                  onTap: () => _showAboutDialog(context),
                ),
                SettingsItem(
                  icon: Icons.help,
                  title: '帮助与支持',
                  subtitle: '使用指南和常见问题',
                  onTap: () {
                    // TODO: 打开帮助页面
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(const SnackBar(content: Text('帮助页面开发中')));
                  },
                ),
                SettingsItem(
                  icon: Icons.feedback,
                  title: '反馈建议',
                  subtitle: '向我们提供反馈',
                  onTap: () {
                    // TODO: 打开反馈页面
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(const SnackBar(content: Text('反馈功能开发中')));
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }

  String _getFontSizeText(double size) {
    if (size <= 12) return '小';
    if (size <= 14) return '中';
    if (size <= 16) return '大';
    return '特大';
  }

  String _getLineHeightText(double height) {
    if (height <= 1.2) return '紧密';
    if (height <= 1.5) return '标准';
    if (height <= 1.8) return '宽松';
    return '很宽松';
  }

  void _showBackupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('备份数据'),
        content: const Text('确定要备份所有数据吗？备份文件将保存到下载文件夹。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现数据备份
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('数据备份功能开发中')));
            },
            child: const Text('备份'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('恢复数据'),
        content: const Text('选择备份文件来恢复数据。注意：这将覆盖当前所有数据。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现数据恢复
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('数据恢复功能开发中')));
            },
            child: const Text('选择文件'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除所有数据'),
        content: const Text('确定要删除所有本地数据吗？此操作不可撤销！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现数据清除
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('数据清除功能开发中')));
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'NovelICE',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.book, size: 48),
      children: [
        const Text('NovelICE 是一个专为小说创作者设计的集成创作环境。'),
        const SizedBox(height: 16),
        const Text('功能特色：'),
        const Text('• 小说作品和章节管理'),
        const Text('• Wiki系统（角色、世界观等）'),
        const Text('• 素材库（灵感、想法等）'),
        const Text('• 写作统计和目标追踪'),
        const Text('• 本地存储，支持离线使用'),
        const Text('• 响应式设计，支持多平台'),
      ],
    );
  }
}
