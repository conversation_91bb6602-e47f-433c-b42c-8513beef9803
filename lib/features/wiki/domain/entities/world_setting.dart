import 'package:json_annotation/json_annotation.dart';

part 'world_setting.g.dart';

/// 世界观设定实体类
@JsonSerializable()
class WorldSetting {
  /// 唯一标识符
  final String id;
  
  /// 所属小说ID
  final String novelId;
  
  /// 设定名称
  final String name;
  
  /// 设定类型
  final WorldSettingType type;
  
  /// 设定描述
  final String description;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后修改时间
  final DateTime updatedAt;
  
  /// 父级设定ID（用于层级结构）
  final String? parentId;
  
  /// 地理信息
  final GeographicInfo? geographicInfo;
  
  /// 历史信息
  final HistoricalInfo? historicalInfo;
  
  /// 文化信息
  final CulturalInfo? culturalInfo;
  
  /// 政治信息
  final PoliticalInfo? politicalInfo;
  
  /// 经济信息
  final EconomicInfo? economicInfo;
  
  /// 科技信息
  final TechnologicalInfo? technologicalInfo;
  
  /// 魔法/超自然信息
  final MagicalInfo? magicalInfo;
  
  /// 相关图片路径
  final List<String> imagePaths;
  
  /// 标签
  final List<String> tags;
  
  /// 备注
  final String? notes;

  const WorldSetting({
    required this.id,
    required this.novelId,
    required this.name,
    required this.type,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    this.parentId,
    this.geographicInfo,
    this.historicalInfo,
    this.culturalInfo,
    this.politicalInfo,
    this.economicInfo,
    this.technologicalInfo,
    this.magicalInfo,
    required this.imagePaths,
    required this.tags,
    this.notes,
  });

  /// 从JSON创建WorldSetting实例
  factory WorldSetting.fromJson(Map<String, dynamic> json) => 
      _$WorldSettingFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$WorldSettingToJson(this);

  /// 复制并修改部分属性
  WorldSetting copyWith({
    String? id,
    String? novelId,
    String? name,
    WorldSettingType? type,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? parentId,
    GeographicInfo? geographicInfo,
    HistoricalInfo? historicalInfo,
    CulturalInfo? culturalInfo,
    PoliticalInfo? politicalInfo,
    EconomicInfo? economicInfo,
    TechnologicalInfo? technologicalInfo,
    MagicalInfo? magicalInfo,
    List<String>? imagePaths,
    List<String>? tags,
    String? notes,
  }) {
    return WorldSetting(
      id: id ?? this.id,
      novelId: novelId ?? this.novelId,
      name: name ?? this.name,
      type: type ?? this.type,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      parentId: parentId ?? this.parentId,
      geographicInfo: geographicInfo ?? this.geographicInfo,
      historicalInfo: historicalInfo ?? this.historicalInfo,
      culturalInfo: culturalInfo ?? this.culturalInfo,
      politicalInfo: politicalInfo ?? this.politicalInfo,
      economicInfo: economicInfo ?? this.economicInfo,
      technologicalInfo: technologicalInfo ?? this.technologicalInfo,
      magicalInfo: magicalInfo ?? this.magicalInfo,
      imagePaths: imagePaths ?? this.imagePaths,
      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorldSetting && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'WorldSetting(id: $id, name: $name, type: $type)';
}

/// 世界观设定类型枚举
enum WorldSettingType {
  /// 世界/宇宙
  world,
  /// 大陆
  continent,
  /// 国家
  country,
  /// 城市
  city,
  /// 地点
  location,
  /// 组织
  organization,
  /// 种族
  race,
  /// 宗教
  religion,
  /// 语言
  language,
  /// 货币
  currency,
  /// 法律
  law,
  /// 其他
  other,
}

/// 地理信息
@JsonSerializable()
class GeographicInfo {
  /// 气候
  final String? climate;
  
  /// 地形
  final String? terrain;
  
  /// 面积
  final String? area;
  
  /// 人口
  final String? population;
  
  /// 主要城市
  final List<String> majorCities;
  
  /// 自然资源
  final List<String> naturalResources;

  const GeographicInfo({
    this.climate,
    this.terrain,
    this.area,
    this.population,
    required this.majorCities,
    required this.naturalResources,
  });

  factory GeographicInfo.fromJson(Map<String, dynamic> json) => 
      _$GeographicInfoFromJson(json);

  Map<String, dynamic> toJson() => _$GeographicInfoToJson(this);
}

/// 历史信息
@JsonSerializable()
class HistoricalInfo {
  /// 建立时间
  final String? foundedDate;
  
  /// 重要历史事件
  final List<String> majorEvents;
  
  /// 历史人物
  final List<String> historicalFigures;
  
  /// 历史时期
  final List<String> historicalPeriods;

  const HistoricalInfo({
    this.foundedDate,
    required this.majorEvents,
    required this.historicalFigures,
    required this.historicalPeriods,
  });

  factory HistoricalInfo.fromJson(Map<String, dynamic> json) => 
      _$HistoricalInfoFromJson(json);

  Map<String, dynamic> toJson() => _$HistoricalInfoToJson(this);
}

/// 文化信息
@JsonSerializable()
class CulturalInfo {
  /// 主要语言
  final List<String> languages;
  
  /// 宗教信仰
  final List<String> religions;
  
  /// 传统节日
  final List<String> festivals;
  
  /// 文化特色
  final List<String> culturalTraits;
  
  /// 艺术形式
  final List<String> artForms;

  const CulturalInfo({
    required this.languages,
    required this.religions,
    required this.festivals,
    required this.culturalTraits,
    required this.artForms,
  });

  factory CulturalInfo.fromJson(Map<String, dynamic> json) => 
      _$CulturalInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CulturalInfoToJson(this);
}

/// 政治信息
@JsonSerializable()
class PoliticalInfo {
  /// 政治制度
  final String? governmentType;
  
  /// 统治者
  final String? ruler;
  
  /// 主要政治组织
  final List<String> politicalOrganizations;
  
  /// 法律制度
  final String? legalSystem;

  const PoliticalInfo({
    this.governmentType,
    this.ruler,
    required this.politicalOrganizations,
    this.legalSystem,
  });

  factory PoliticalInfo.fromJson(Map<String, dynamic> json) => 
      _$PoliticalInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PoliticalInfoToJson(this);
}

/// 经济信息
@JsonSerializable()
class EconomicInfo {
  /// 主要产业
  final List<String> majorIndustries;
  
  /// 货币制度
  final String? currency;
  
  /// 贸易伙伴
  final List<String> tradePartners;
  
  /// 经济水平
  final String? economicLevel;

  const EconomicInfo({
    required this.majorIndustries,
    this.currency,
    required this.tradePartners,
    this.economicLevel,
  });

  factory EconomicInfo.fromJson(Map<String, dynamic> json) => 
      _$EconomicInfoFromJson(json);

  Map<String, dynamic> toJson() => _$EconomicInfoToJson(this);
}

/// 科技信息
@JsonSerializable()
class TechnologicalInfo {
  /// 科技水平
  final String? techLevel;
  
  /// 主要科技
  final List<String> majorTechnologies;
  
  /// 交通工具
  final List<String> transportation;
  
  /// 通讯方式
  final List<String> communication;

  const TechnologicalInfo({
    this.techLevel,
    required this.majorTechnologies,
    required this.transportation,
    required this.communication,
  });

  factory TechnologicalInfo.fromJson(Map<String, dynamic> json) => 
      _$TechnologicalInfoFromJson(json);

  Map<String, dynamic> toJson() => _$TechnologicalInfoToJson(this);
}

/// 魔法/超自然信息
@JsonSerializable()
class MagicalInfo {
  /// 魔法系统
  final String? magicSystem;
  
  /// 魔法类型
  final List<String> magicTypes;
  
  /// 超自然生物
  final List<String> supernaturalBeings;
  
  /// 魔法物品
  final List<String> magicalItems;

  const MagicalInfo({
    this.magicSystem,
    required this.magicTypes,
    required this.supernaturalBeings,
    required this.magicalItems,
  });

  factory MagicalInfo.fromJson(Map<String, dynamic> json) => 
      _$MagicalInfoFromJson(json);

  Map<String, dynamic> toJson() => _$MagicalInfoToJson(this);
}
