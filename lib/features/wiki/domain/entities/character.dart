import 'package:json_annotation/json_annotation.dart';

part 'character.g.dart';

/// 角色实体类
@JsonSerializable()
class Character {
  /// 唯一标识符
  final String id;
  
  /// 所属小说ID
  final String novelId;
  
  /// 角色姓名
  final String name;
  
  /// 角色别名/昵称
  final List<String> aliases;
  
  /// 角色描述
  final String description;
  
  /// 角色头像路径
  final String? avatarPath;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后修改时间
  final DateTime updatedAt;
  
  /// 角色类型
  final CharacterType type;
  
  /// 角色重要性
  final CharacterImportance importance;
  
  /// 基本信息
  final CharacterBasicInfo basicInfo;
  
  /// 外貌特征
  final CharacterAppearance appearance;
  
  /// 性格特征
  final CharacterPersonality personality;
  
  /// 背景故事
  final String? backstory;
  
  /// 角色关系
  final List<CharacterRelationship> relationships;
  
  /// 角色技能/能力
  final List<String> skills;
  
  /// 角色目标/动机
  final String? motivation;
  
  /// 角色弱点/恐惧
  final String? weakness;
  
  /// 角色发展弧线
  final String? characterArc;
  
  /// 备注
  final String? notes;

  const Character({
    required this.id,
    required this.novelId,
    required this.name,
    required this.aliases,
    required this.description,
    this.avatarPath,
    required this.createdAt,
    required this.updatedAt,
    required this.type,
    required this.importance,
    required this.basicInfo,
    required this.appearance,
    required this.personality,
    this.backstory,
    required this.relationships,
    required this.skills,
    this.motivation,
    this.weakness,
    this.characterArc,
    this.notes,
  });

  /// 从JSON创建Character实例
  factory Character.fromJson(Map<String, dynamic> json) => _$CharacterFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$CharacterToJson(this);

  /// 复制并修改部分属性
  Character copyWith({
    String? id,
    String? novelId,
    String? name,
    List<String>? aliases,
    String? description,
    String? avatarPath,
    DateTime? createdAt,
    DateTime? updatedAt,
    CharacterType? type,
    CharacterImportance? importance,
    CharacterBasicInfo? basicInfo,
    CharacterAppearance? appearance,
    CharacterPersonality? personality,
    String? backstory,
    List<CharacterRelationship>? relationships,
    List<String>? skills,
    String? motivation,
    String? weakness,
    String? characterArc,
    String? notes,
  }) {
    return Character(
      id: id ?? this.id,
      novelId: novelId ?? this.novelId,
      name: name ?? this.name,
      aliases: aliases ?? this.aliases,
      description: description ?? this.description,
      avatarPath: avatarPath ?? this.avatarPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      importance: importance ?? this.importance,
      basicInfo: basicInfo ?? this.basicInfo,
      appearance: appearance ?? this.appearance,
      personality: personality ?? this.personality,
      backstory: backstory ?? this.backstory,
      relationships: relationships ?? this.relationships,
      skills: skills ?? this.skills,
      motivation: motivation ?? this.motivation,
      weakness: weakness ?? this.weakness,
      characterArc: characterArc ?? this.characterArc,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Character && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Character(id: $id, name: $name, type: $type)';
}

/// 角色类型枚举
enum CharacterType {
  /// 主角
  protagonist,
  /// 反派
  antagonist,
  /// 配角
  supporting,
  /// 路人
  background,
}

/// 角色重要性枚举
enum CharacterImportance {
  /// 主要角色
  major,
  /// 次要角色
  minor,
  /// 临时角色
  temporary,
}

/// 角色基本信息
@JsonSerializable()
class CharacterBasicInfo {
  /// 年龄
  final int? age;
  
  /// 性别
  final String? gender;
  
  /// 生日
  final DateTime? birthday;
  
  /// 出生地
  final String? birthplace;
  
  /// 职业
  final String? occupation;
  
  /// 种族
  final String? race;
  
  /// 国籍
  final String? nationality;

  const CharacterBasicInfo({
    this.age,
    this.gender,
    this.birthday,
    this.birthplace,
    this.occupation,
    this.race,
    this.nationality,
  });

  factory CharacterBasicInfo.fromJson(Map<String, dynamic> json) => 
      _$CharacterBasicInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CharacterBasicInfoToJson(this);
}

/// 角色外貌特征
@JsonSerializable()
class CharacterAppearance {
  /// 身高
  final String? height;
  
  /// 体重
  final String? weight;
  
  /// 发色
  final String? hairColor;
  
  /// 眼色
  final String? eyeColor;
  
  /// 肤色
  final String? skinColor;
  
  /// 体型
  final String? bodyType;
  
  /// 特殊标记
  final List<String> specialMarks;
  
  /// 服装风格
  final String? clothingStyle;

  const CharacterAppearance({
    this.height,
    this.weight,
    this.hairColor,
    this.eyeColor,
    this.skinColor,
    this.bodyType,
    required this.specialMarks,
    this.clothingStyle,
  });

  factory CharacterAppearance.fromJson(Map<String, dynamic> json) => 
      _$CharacterAppearanceFromJson(json);

  Map<String, dynamic> toJson() => _$CharacterAppearanceToJson(this);
}

/// 角色性格特征
@JsonSerializable()
class CharacterPersonality {
  /// 性格特点
  final List<String> traits;
  
  /// 爱好
  final List<String> hobbies;
  
  /// 习惯
  final List<String> habits;
  
  /// 口头禅
  final List<String> catchphrases;
  
  /// 恐惧
  final List<String> fears;
  
  /// 价值观
  final String? values;

  const CharacterPersonality({
    required this.traits,
    required this.hobbies,
    required this.habits,
    required this.catchphrases,
    required this.fears,
    this.values,
  });

  factory CharacterPersonality.fromJson(Map<String, dynamic> json) => 
      _$CharacterPersonalityFromJson(json);

  Map<String, dynamic> toJson() => _$CharacterPersonalityToJson(this);
}

/// 角色关系
@JsonSerializable()
class CharacterRelationship {
  /// 关联角色ID
  final String characterId;
  
  /// 关系类型
  final String relationshipType;
  
  /// 关系描述
  final String description;

  const CharacterRelationship({
    required this.characterId,
    required this.relationshipType,
    required this.description,
  });

  factory CharacterRelationship.fromJson(Map<String, dynamic> json) => 
      _$CharacterRelationshipFromJson(json);

  Map<String, dynamic> toJson() => _$CharacterRelationshipToJson(this);
}
