// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timeline.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Timeline _$TimelineFromJson(Map<String, dynamic> json) => Timeline(
  id: json['id'] as String,
  novelId: json['novelId'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  type: $enumDecode(_$TimelineTypeEnumMap, json['type']),
  events: (json['events'] as List<dynamic>)
      .map((e) => TimelineEvent.fromJson(e as Map<String, dynamic>))
      .toList(),
  color: (json['color'] as num).toInt(),
  isVisible: json['isVisible'] as bool,
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$TimelineToJson(Timeline instance) => <String, dynamic>{
  'id': instance.id,
  'novelId': instance.novelId,
  'name': instance.name,
  'description': instance.description,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'type': _$TimelineTypeEnumMap[instance.type]!,
  'events': instance.events,
  'color': instance.color,
  'isVisible': instance.isVisible,
  'notes': instance.notes,
};

const _$TimelineTypeEnumMap = {
  TimelineType.main: 'main',
  TimelineType.character: 'character',
  TimelineType.world: 'world',
  TimelineType.custom: 'custom',
};

TimelineEvent _$TimelineEventFromJson(Map<String, dynamic> json) =>
    TimelineEvent(
      id: json['id'] as String,
      timelineId: json['timelineId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      storyTime: StoryTime.fromJson(json['storyTime'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      type: $enumDecode(_$EventTypeEnumMap, json['type']),
      importance: $enumDecode(_$EventImportanceEnumMap, json['importance']),
      relatedCharacterIds: (json['relatedCharacterIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      relatedLocationIds: (json['relatedLocationIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      relatedChapterIds: (json['relatedChapterIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      outcome: json['outcome'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$TimelineEventToJson(TimelineEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'timelineId': instance.timelineId,
      'title': instance.title,
      'description': instance.description,
      'storyTime': instance.storyTime,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'type': _$EventTypeEnumMap[instance.type]!,
      'importance': _$EventImportanceEnumMap[instance.importance]!,
      'relatedCharacterIds': instance.relatedCharacterIds,
      'relatedLocationIds': instance.relatedLocationIds,
      'relatedChapterIds': instance.relatedChapterIds,
      'outcome': instance.outcome,
      'notes': instance.notes,
    };

const _$EventTypeEnumMap = {
  EventType.plot: 'plot',
  EventType.character: 'character',
  EventType.historical: 'historical',
  EventType.world: 'world',
  EventType.other: 'other',
};

const _$EventImportanceEnumMap = {
  EventImportance.critical: 'critical',
  EventImportance.important: 'important',
  EventImportance.normal: 'normal',
  EventImportance.minor: 'minor',
};

StoryTime _$StoryTimeFromJson(Map<String, dynamic> json) => StoryTime(
  year: (json['year'] as num?)?.toInt(),
  month: (json['month'] as num?)?.toInt(),
  day: (json['day'] as num?)?.toInt(),
  hour: (json['hour'] as num?)?.toInt(),
  minute: (json['minute'] as num?)?.toInt(),
  era: json['era'] as String?,
  season: json['season'] as String?,
  description: json['description'] as String?,
  relativeTime: json['relativeTime'] as String?,
);

Map<String, dynamic> _$StoryTimeToJson(StoryTime instance) => <String, dynamic>{
  'year': instance.year,
  'month': instance.month,
  'day': instance.day,
  'hour': instance.hour,
  'minute': instance.minute,
  'era': instance.era,
  'season': instance.season,
  'description': instance.description,
  'relativeTime': instance.relativeTime,
};
