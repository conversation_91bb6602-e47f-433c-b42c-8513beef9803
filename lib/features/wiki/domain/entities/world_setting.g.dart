// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'world_setting.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorldSetting _$WorldSettingFromJson(Map<String, dynamic> json) => WorldSetting(
  id: json['id'] as String,
  novelId: json['novelId'] as String,
  name: json['name'] as String,
  type: $enumDecode(_$WorldSettingTypeEnumMap, json['type']),
  description: json['description'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  parentId: json['parentId'] as String?,
  geographicInfo: json['geographicInfo'] == null
      ? null
      : GeographicInfo.fromJson(json['geographicInfo'] as Map<String, dynamic>),
  historicalInfo: json['historicalInfo'] == null
      ? null
      : HistoricalInfo.fromJson(json['historicalInfo'] as Map<String, dynamic>),
  culturalInfo: json['culturalInfo'] == null
      ? null
      : CulturalInfo.fromJson(json['culturalInfo'] as Map<String, dynamic>),
  politicalInfo: json['politicalInfo'] == null
      ? null
      : PoliticalInfo.fromJson(json['politicalInfo'] as Map<String, dynamic>),
  economicInfo: json['economicInfo'] == null
      ? null
      : EconomicInfo.fromJson(json['economicInfo'] as Map<String, dynamic>),
  technologicalInfo: json['technologicalInfo'] == null
      ? null
      : TechnologicalInfo.fromJson(
          json['technologicalInfo'] as Map<String, dynamic>,
        ),
  magicalInfo: json['magicalInfo'] == null
      ? null
      : MagicalInfo.fromJson(json['magicalInfo'] as Map<String, dynamic>),
  imagePaths: (json['imagePaths'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$WorldSettingToJson(WorldSetting instance) =>
    <String, dynamic>{
      'id': instance.id,
      'novelId': instance.novelId,
      'name': instance.name,
      'type': _$WorldSettingTypeEnumMap[instance.type]!,
      'description': instance.description,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'parentId': instance.parentId,
      'geographicInfo': instance.geographicInfo,
      'historicalInfo': instance.historicalInfo,
      'culturalInfo': instance.culturalInfo,
      'politicalInfo': instance.politicalInfo,
      'economicInfo': instance.economicInfo,
      'technologicalInfo': instance.technologicalInfo,
      'magicalInfo': instance.magicalInfo,
      'imagePaths': instance.imagePaths,
      'tags': instance.tags,
      'notes': instance.notes,
    };

const _$WorldSettingTypeEnumMap = {
  WorldSettingType.world: 'world',
  WorldSettingType.continent: 'continent',
  WorldSettingType.country: 'country',
  WorldSettingType.city: 'city',
  WorldSettingType.location: 'location',
  WorldSettingType.organization: 'organization',
  WorldSettingType.race: 'race',
  WorldSettingType.religion: 'religion',
  WorldSettingType.language: 'language',
  WorldSettingType.currency: 'currency',
  WorldSettingType.law: 'law',
  WorldSettingType.other: 'other',
};

GeographicInfo _$GeographicInfoFromJson(Map<String, dynamic> json) =>
    GeographicInfo(
      climate: json['climate'] as String?,
      terrain: json['terrain'] as String?,
      area: json['area'] as String?,
      population: json['population'] as String?,
      majorCities: (json['majorCities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      naturalResources: (json['naturalResources'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$GeographicInfoToJson(GeographicInfo instance) =>
    <String, dynamic>{
      'climate': instance.climate,
      'terrain': instance.terrain,
      'area': instance.area,
      'population': instance.population,
      'majorCities': instance.majorCities,
      'naturalResources': instance.naturalResources,
    };

HistoricalInfo _$HistoricalInfoFromJson(Map<String, dynamic> json) =>
    HistoricalInfo(
      foundedDate: json['foundedDate'] as String?,
      majorEvents: (json['majorEvents'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      historicalFigures: (json['historicalFigures'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      historicalPeriods: (json['historicalPeriods'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$HistoricalInfoToJson(HistoricalInfo instance) =>
    <String, dynamic>{
      'foundedDate': instance.foundedDate,
      'majorEvents': instance.majorEvents,
      'historicalFigures': instance.historicalFigures,
      'historicalPeriods': instance.historicalPeriods,
    };

CulturalInfo _$CulturalInfoFromJson(Map<String, dynamic> json) => CulturalInfo(
  languages: (json['languages'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  religions: (json['religions'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  festivals: (json['festivals'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  culturalTraits: (json['culturalTraits'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  artForms: (json['artForms'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$CulturalInfoToJson(CulturalInfo instance) =>
    <String, dynamic>{
      'languages': instance.languages,
      'religions': instance.religions,
      'festivals': instance.festivals,
      'culturalTraits': instance.culturalTraits,
      'artForms': instance.artForms,
    };

PoliticalInfo _$PoliticalInfoFromJson(Map<String, dynamic> json) =>
    PoliticalInfo(
      governmentType: json['governmentType'] as String?,
      ruler: json['ruler'] as String?,
      politicalOrganizations: (json['politicalOrganizations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      legalSystem: json['legalSystem'] as String?,
    );

Map<String, dynamic> _$PoliticalInfoToJson(PoliticalInfo instance) =>
    <String, dynamic>{
      'governmentType': instance.governmentType,
      'ruler': instance.ruler,
      'politicalOrganizations': instance.politicalOrganizations,
      'legalSystem': instance.legalSystem,
    };

EconomicInfo _$EconomicInfoFromJson(Map<String, dynamic> json) => EconomicInfo(
  majorIndustries: (json['majorIndustries'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  currency: json['currency'] as String?,
  tradePartners: (json['tradePartners'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  economicLevel: json['economicLevel'] as String?,
);

Map<String, dynamic> _$EconomicInfoToJson(EconomicInfo instance) =>
    <String, dynamic>{
      'majorIndustries': instance.majorIndustries,
      'currency': instance.currency,
      'tradePartners': instance.tradePartners,
      'economicLevel': instance.economicLevel,
    };

TechnologicalInfo _$TechnologicalInfoFromJson(Map<String, dynamic> json) =>
    TechnologicalInfo(
      techLevel: json['techLevel'] as String?,
      majorTechnologies: (json['majorTechnologies'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      transportation: (json['transportation'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      communication: (json['communication'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$TechnologicalInfoToJson(TechnologicalInfo instance) =>
    <String, dynamic>{
      'techLevel': instance.techLevel,
      'majorTechnologies': instance.majorTechnologies,
      'transportation': instance.transportation,
      'communication': instance.communication,
    };

MagicalInfo _$MagicalInfoFromJson(Map<String, dynamic> json) => MagicalInfo(
  magicSystem: json['magicSystem'] as String?,
  magicTypes: (json['magicTypes'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  supernaturalBeings: (json['supernaturalBeings'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  magicalItems: (json['magicalItems'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$MagicalInfoToJson(MagicalInfo instance) =>
    <String, dynamic>{
      'magicSystem': instance.magicSystem,
      'magicTypes': instance.magicTypes,
      'supernaturalBeings': instance.supernaturalBeings,
      'magicalItems': instance.magicalItems,
    };
