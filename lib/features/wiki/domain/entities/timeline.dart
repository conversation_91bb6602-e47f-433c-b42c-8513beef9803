import 'package:json_annotation/json_annotation.dart';

part 'timeline.g.dart';

/// 时间线实体类
@JsonSerializable()
class Timeline {
  /// 唯一标识符
  final String id;

  /// 所属小说ID
  final String novelId;

  /// 时间线名称
  final String name;

  /// 时间线描述
  final String description;

  /// 创建时间
  final DateTime createdAt;

  /// 最后修改时间
  final DateTime updatedAt;

  /// 时间线类型
  final TimelineType type;

  /// 时间线事件列表
  final List<TimelineEvent> events;

  /// 时间线颜色（用于UI显示）
  final int color;

  /// 是否可见
  final bool isVisible;

  /// 备注
  final String? notes;

  const Timeline({
    required this.id,
    required this.novelId,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.type,
    required this.events,
    required this.color,
    required this.isVisible,
    this.notes,
  });

  /// 从JSON创建Timeline实例
  factory Timeline.fromJson(Map<String, dynamic> json) =>
      _$TimelineFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$TimelineToJson(this);

  /// 复制并修改部分属性
  Timeline copyWith({
    String? id,
    String? novelId,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    TimelineType? type,
    List<TimelineEvent>? events,
    int? color,
    bool? isVisible,
    String? notes,
  }) {
    return Timeline(
      id: id ?? this.id,
      novelId: novelId ?? this.novelId,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      events: events ?? this.events,
      color: color ?? this.color,
      isVisible: isVisible ?? this.isVisible,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Timeline && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Timeline(id: $id, name: $name, type: $type)';
}

/// 时间线类型枚举
enum TimelineType {
  /// 主要情节线
  main,

  /// 角色发展线
  character,

  /// 世界历史线
  world,

  /// 自定义时间线
  custom,
}

/// 时间线事件实体类
@JsonSerializable()
class TimelineEvent {
  /// 唯一标识符
  final String id;

  /// 所属时间线ID
  final String timelineId;

  /// 事件标题
  final String title;

  /// 事件描述
  final String description;

  /// 事件时间（故事内时间）
  final StoryTime storyTime;

  /// 创建时间
  final DateTime createdAt;

  /// 最后修改时间
  final DateTime updatedAt;

  /// 事件类型
  final EventType type;

  /// 事件重要性
  final EventImportance importance;

  /// 相关角色ID列表
  final List<String> relatedCharacterIds;

  /// 相关地点ID列表
  final List<String> relatedLocationIds;

  /// 相关章节ID列表
  final List<String> relatedChapterIds;

  /// 事件结果/影响
  final String? outcome;

  /// 备注
  final String? notes;

  const TimelineEvent({
    required this.id,
    required this.timelineId,
    required this.title,
    required this.description,
    required this.storyTime,
    required this.createdAt,
    required this.updatedAt,
    required this.type,
    required this.importance,
    required this.relatedCharacterIds,
    required this.relatedLocationIds,
    required this.relatedChapterIds,
    this.outcome,
    this.notes,
  });

  /// 从JSON创建TimelineEvent实例
  factory TimelineEvent.fromJson(Map<String, dynamic> json) =>
      _$TimelineEventFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$TimelineEventToJson(this);

  /// 复制并修改部分属性
  TimelineEvent copyWith({
    String? id,
    String? timelineId,
    String? title,
    String? description,
    StoryTime? storyTime,
    DateTime? createdAt,
    DateTime? updatedAt,
    EventType? type,
    EventImportance? importance,
    List<String>? relatedCharacterIds,
    List<String>? relatedLocationIds,
    List<String>? relatedChapterIds,
    String? outcome,
    String? notes,
  }) {
    return TimelineEvent(
      id: id ?? this.id,
      timelineId: timelineId ?? this.timelineId,
      title: title ?? this.title,
      description: description ?? this.description,
      storyTime: storyTime ?? this.storyTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      importance: importance ?? this.importance,
      relatedCharacterIds: relatedCharacterIds ?? this.relatedCharacterIds,
      relatedLocationIds: relatedLocationIds ?? this.relatedLocationIds,
      relatedChapterIds: relatedChapterIds ?? this.relatedChapterIds,
      outcome: outcome ?? this.outcome,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimelineEvent &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'TimelineEvent(id: $id, title: $title, type: $type)';
}

/// 事件类型枚举
enum EventType {
  /// 情节事件
  plot,

  /// 角色事件
  character,

  /// 历史事件
  historical,

  /// 世界事件
  world,

  /// 其他
  other,
}

/// 事件重要性枚举
enum EventImportance {
  /// 关键事件
  critical,

  /// 重要事件
  important,

  /// 一般事件
  normal,

  /// 次要事件
  minor,
}

/// 故事时间
@JsonSerializable()
class StoryTime {
  /// 年
  final int? year;

  /// 月
  final int? month;

  /// 日
  final int? day;

  /// 时
  final int? hour;

  /// 分
  final int? minute;

  /// 时代/纪元
  final String? era;

  /// 季节
  final String? season;

  /// 时间描述（如"春天的早晨"）
  final String? description;

  /// 相对时间（如"三天后"）
  final String? relativeTime;

  const StoryTime({
    this.year,
    this.month,
    this.day,
    this.hour,
    this.minute,
    this.era,
    this.season,
    this.description,
    this.relativeTime,
  });

  /// 从JSON创建StoryTime实例
  factory StoryTime.fromJson(Map<String, dynamic> json) =>
      _$StoryTimeFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$StoryTimeToJson(this);

  /// 复制并修改部分属性
  StoryTime copyWith({
    int? year,
    int? month,
    int? day,
    int? hour,
    int? minute,
    String? era,
    String? season,
    String? description,
    String? relativeTime,
  }) {
    return StoryTime(
      year: year ?? this.year,
      month: month ?? this.month,
      day: day ?? this.day,
      hour: hour ?? this.hour,
      minute: minute ?? this.minute,
      era: era ?? this.era,
      season: season ?? this.season,
      description: description ?? this.description,
      relativeTime: relativeTime ?? this.relativeTime,
    );
  }

  @override
  String toString() {
    final parts = <String>[];

    if (era != null) parts.add(era!);
    if (year != null) parts.add('$year年');
    if (month != null) parts.add('$month月');
    if (day != null) parts.add('$day日');
    if (hour != null && minute != null) {
      parts.add(
        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}',
      );
    } else if (hour != null) {
      parts.add('$hour时');
    }
    if (season != null) parts.add(season!);
    if (description != null) parts.add(description!);
    if (relativeTime != null) parts.add(relativeTime!);

    return parts.isEmpty ? '未指定时间' : parts.join(' ');
  }
}
