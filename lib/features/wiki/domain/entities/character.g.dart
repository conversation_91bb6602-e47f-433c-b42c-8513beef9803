// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'character.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Character _$CharacterFromJson(Map<String, dynamic> json) => Character(
  id: json['id'] as String,
  novelId: json['novelId'] as String,
  name: json['name'] as String,
  aliases: (json['aliases'] as List<dynamic>).map((e) => e as String).toList(),
  description: json['description'] as String,
  avatarPath: json['avatarPath'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  type: $enumDecode(_$CharacterTypeEnumMap, json['type']),
  importance: $enumDecode(_$CharacterImportanceEnumMap, json['importance']),
  basicInfo: CharacterBasicInfo.fromJson(
    json['basicInfo'] as Map<String, dynamic>,
  ),
  appearance: CharacterAppearance.fromJson(
    json['appearance'] as Map<String, dynamic>,
  ),
  personality: CharacterPersonality.fromJson(
    json['personality'] as Map<String, dynamic>,
  ),
  backstory: json['backstory'] as String?,
  relationships: (json['relationships'] as List<dynamic>)
      .map((e) => CharacterRelationship.fromJson(e as Map<String, dynamic>))
      .toList(),
  skills: (json['skills'] as List<dynamic>).map((e) => e as String).toList(),
  motivation: json['motivation'] as String?,
  weakness: json['weakness'] as String?,
  characterArc: json['characterArc'] as String?,
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$CharacterToJson(Character instance) => <String, dynamic>{
  'id': instance.id,
  'novelId': instance.novelId,
  'name': instance.name,
  'aliases': instance.aliases,
  'description': instance.description,
  'avatarPath': instance.avatarPath,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'type': _$CharacterTypeEnumMap[instance.type]!,
  'importance': _$CharacterImportanceEnumMap[instance.importance]!,
  'basicInfo': instance.basicInfo,
  'appearance': instance.appearance,
  'personality': instance.personality,
  'backstory': instance.backstory,
  'relationships': instance.relationships,
  'skills': instance.skills,
  'motivation': instance.motivation,
  'weakness': instance.weakness,
  'characterArc': instance.characterArc,
  'notes': instance.notes,
};

const _$CharacterTypeEnumMap = {
  CharacterType.protagonist: 'protagonist',
  CharacterType.antagonist: 'antagonist',
  CharacterType.supporting: 'supporting',
  CharacterType.background: 'background',
};

const _$CharacterImportanceEnumMap = {
  CharacterImportance.major: 'major',
  CharacterImportance.minor: 'minor',
  CharacterImportance.temporary: 'temporary',
};

CharacterBasicInfo _$CharacterBasicInfoFromJson(Map<String, dynamic> json) =>
    CharacterBasicInfo(
      age: (json['age'] as num?)?.toInt(),
      gender: json['gender'] as String?,
      birthday: json['birthday'] == null
          ? null
          : DateTime.parse(json['birthday'] as String),
      birthplace: json['birthplace'] as String?,
      occupation: json['occupation'] as String?,
      race: json['race'] as String?,
      nationality: json['nationality'] as String?,
    );

Map<String, dynamic> _$CharacterBasicInfoToJson(CharacterBasicInfo instance) =>
    <String, dynamic>{
      'age': instance.age,
      'gender': instance.gender,
      'birthday': instance.birthday?.toIso8601String(),
      'birthplace': instance.birthplace,
      'occupation': instance.occupation,
      'race': instance.race,
      'nationality': instance.nationality,
    };

CharacterAppearance _$CharacterAppearanceFromJson(Map<String, dynamic> json) =>
    CharacterAppearance(
      height: json['height'] as String?,
      weight: json['weight'] as String?,
      hairColor: json['hairColor'] as String?,
      eyeColor: json['eyeColor'] as String?,
      skinColor: json['skinColor'] as String?,
      bodyType: json['bodyType'] as String?,
      specialMarks: (json['specialMarks'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      clothingStyle: json['clothingStyle'] as String?,
    );

Map<String, dynamic> _$CharacterAppearanceToJson(
  CharacterAppearance instance,
) => <String, dynamic>{
  'height': instance.height,
  'weight': instance.weight,
  'hairColor': instance.hairColor,
  'eyeColor': instance.eyeColor,
  'skinColor': instance.skinColor,
  'bodyType': instance.bodyType,
  'specialMarks': instance.specialMarks,
  'clothingStyle': instance.clothingStyle,
};

CharacterPersonality _$CharacterPersonalityFromJson(
  Map<String, dynamic> json,
) => CharacterPersonality(
  traits: (json['traits'] as List<dynamic>).map((e) => e as String).toList(),
  hobbies: (json['hobbies'] as List<dynamic>).map((e) => e as String).toList(),
  habits: (json['habits'] as List<dynamic>).map((e) => e as String).toList(),
  catchphrases: (json['catchphrases'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  fears: (json['fears'] as List<dynamic>).map((e) => e as String).toList(),
  values: json['values'] as String?,
);

Map<String, dynamic> _$CharacterPersonalityToJson(
  CharacterPersonality instance,
) => <String, dynamic>{
  'traits': instance.traits,
  'hobbies': instance.hobbies,
  'habits': instance.habits,
  'catchphrases': instance.catchphrases,
  'fears': instance.fears,
  'values': instance.values,
};

CharacterRelationship _$CharacterRelationshipFromJson(
  Map<String, dynamic> json,
) => CharacterRelationship(
  characterId: json['characterId'] as String,
  relationshipType: json['relationshipType'] as String,
  description: json['description'] as String,
);

Map<String, dynamic> _$CharacterRelationshipToJson(
  CharacterRelationship instance,
) => <String, dynamic>{
  'characterId': instance.characterId,
  'relationshipType': instance.relationshipType,
  'description': instance.description,
};
