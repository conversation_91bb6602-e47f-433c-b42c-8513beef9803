import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../domain/entities/character.dart';
import '../providers/character_provider.dart';

/// 角色创建/编辑对话框
class CharacterCreateDialog extends StatefulWidget {
  final Character? character; // 如果为null则是创建，否则是编辑

  const CharacterCreateDialog({super.key, this.character});

  @override
  State<CharacterCreateDialog> createState() => _CharacterCreateDialogState();
}

class _CharacterCreateDialogState extends State<CharacterCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _ageController = TextEditingController();
  final _genderController = TextEditingController();
  final _appearanceController = TextEditingController();
  final _personalityController = TextEditingController();
  final _backgroundController = TextEditingController();
  final _tagsController = TextEditingController();

  CharacterType _type = CharacterType.supporting;
  CharacterImportance _importance = CharacterImportance.minor;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.character != null) {
      _nameController.text = widget.character!.name;
      _descriptionController.text = widget.character!.description;
      _ageController.text = widget.character!.basicInfo.age?.toString() ?? '';
      _genderController.text = widget.character!.basicInfo.gender ?? '';
      _personalityController.text = widget.character!.personality.traits.join(
        ', ',
      );
      _backgroundController.text = widget.character!.backstory ?? '';
      _tagsController.text = widget.character!.skills.join(', ');
      _type = widget.character!.type;
      _importance = widget.character!.importance;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _ageController.dispose();
    _genderController.dispose();
    _appearanceController.dispose();
    _personalityController.dispose();
    _backgroundController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.character != null;

    return AlertDialog(
      title: Text(isEditing ? '编辑角色' : '创建新角色'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 基本信息
                _buildSectionTitle('基本信息'),
                const SizedBox(height: 16),

                // 姓名
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: '角色姓名',
                    hintText: '请输入角色姓名',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入角色姓名';
                    }
                    return null;
                  },
                  maxLength: 50,
                ),
                const SizedBox(height: 16),

                // 角色类型
                DropdownButtonFormField<CharacterType>(
                  value: _type,
                  decoration: const InputDecoration(
                    labelText: '角色类型',
                    border: OutlineInputBorder(),
                  ),
                  items: CharacterType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _type = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // 角色重要性
                DropdownButtonFormField<CharacterImportance>(
                  value: _importance,
                  decoration: const InputDecoration(
                    labelText: '角色重要性',
                    border: OutlineInputBorder(),
                  ),
                  items: CharacterImportance.values.map((importance) {
                    return DropdownMenuItem(
                      value: importance,
                      child: Text(_getImportanceText(importance)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _importance = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // 描述
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: '角色描述',
                    hintText: '请输入角色的简要描述',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  maxLength: 500,
                ),
                const SizedBox(height: 16),

                // 年龄和性别
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _ageController,
                        decoration: const InputDecoration(
                          labelText: '年龄',
                          hintText: '年龄（可选）',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final age = int.tryParse(value);
                            if (age == null || age <= 0 || age > 1000) {
                              return '请输入有效年龄';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _genderController,
                        decoration: const InputDecoration(
                          labelText: '性别',
                          hintText: '性别（可选）',
                          border: OutlineInputBorder(),
                        ),
                        maxLength: 10,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // 详细信息
                _buildSectionTitle('详细信息'),
                const SizedBox(height: 16),

                // 外貌
                TextFormField(
                  controller: _appearanceController,
                  decoration: const InputDecoration(
                    labelText: '外貌描述',
                    hintText: '描述角色的外貌特征（可选）',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  maxLength: 500,
                ),
                const SizedBox(height: 16),

                // 性格
                TextFormField(
                  controller: _personalityController,
                  decoration: const InputDecoration(
                    labelText: '性格特点',
                    hintText: '描述角色的性格特点（可选）',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  maxLength: 500,
                ),
                const SizedBox(height: 16),

                // 背景
                TextFormField(
                  controller: _backgroundController,
                  decoration: const InputDecoration(
                    labelText: '背景故事',
                    hintText: '描述角色的背景故事（可选）',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 4,
                  maxLength: 1000,
                ),
                const SizedBox(height: 16),

                // 标签
                TextFormField(
                  controller: _tagsController,
                  decoration: const InputDecoration(
                    labelText: '标签',
                    hintText: '用逗号分隔多个标签（可选）',
                    border: OutlineInputBorder(),
                  ),
                  maxLength: 200,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCharacter,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? '保存' : '创建'),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Divider(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
          ),
        ),
      ],
    );
  }

  String _getTypeText(CharacterType type) {
    switch (type) {
      case CharacterType.protagonist:
        return '主角';
      case CharacterType.antagonist:
        return '反派';
      case CharacterType.supporting:
        return '配角';
      case CharacterType.background:
        return '背景角色';
    }
  }

  String _getImportanceText(CharacterImportance importance) {
    switch (importance) {
      case CharacterImportance.major:
        return '主要角色';
      case CharacterImportance.minor:
        return '次要角色';
      case CharacterImportance.temporary:
        return '临时角色';
    }
  }

  Future<void> _saveCharacter() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim();
      final age = int.tryParse(_ageController.text);
      final gender = _genderController.text.trim();
      final appearance = _appearanceController.text.trim();
      final personality = _personalityController.text.trim();
      final background = _backgroundController.text.trim();
      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      final provider = context.read<CharacterProvider>();

      if (widget.character != null) {
        // 编辑现有角色
        final updatedCharacter = widget.character!.copyWith(
          name: name,
          description: description,
          type: _type,
          importance: _importance,
          basicInfo: CharacterBasicInfo(
            age: age,
            gender: gender.isEmpty ? null : gender,
          ),
          personality: CharacterPersonality(
            traits: personality.isEmpty ? [] : [personality],
            hobbies: [],
            habits: [],
            catchphrases: [],
            fears: [],
          ),
          backstory: background.isEmpty ? null : background,
          skills: tags,
          updatedAt: DateTime.now(),
        );
        await provider.updateCharacter(updatedCharacter);
      } else {
        // 创建新角色
        await provider.createCharacter(
          name: name,
          description: description,
          type: _type,
          importance: _importance,
          age: age,
          gender: gender.isEmpty ? null : gender,
          personality: personality,
          background: background.isEmpty ? null : background,
          skills: tags,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.character != null ? '角色已更新' : '角色创建成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('操作失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
