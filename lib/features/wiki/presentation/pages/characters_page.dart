import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/theme/app_theme.dart';
import '../providers/character_provider.dart';
import '../widgets/character_card.dart';
import '../widgets/character_create_dialog.dart';
import '../../domain/entities/character.dart';

/// 角色管理页面
class CharactersPage extends StatefulWidget {
  const CharactersPage({super.key});

  @override
  State<CharactersPage> createState() => _CharactersPageState();
}

class _CharactersPageState extends State<CharactersPage> {
  String _searchQuery = '';
  CharacterType? _filterType;
  String _sortBy = 'name';
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CharacterProvider>().loadCharacters();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('角色管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(icon: const Icon(Icons.sort), onPressed: _showSortDialog),
          IconButton(icon: const Icon(Icons.add), onPressed: _showCreateDialog),
        ],
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
      floatingActionButton: AppTheme.isMobile(context)
          ? FloatingActionButton(
              onPressed: _showCreateDialog,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildMobileLayout() {
    return Consumer<CharacterProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final characters = _getFilteredCharacters(provider.characters);

        if (characters.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadCharacters(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: characters.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: CharacterCard(
                  character: characters[index],
                  onTap: () => _openCharacter(characters[index]),
                  onEdit: () => _editCharacter(characters[index]),
                  onDelete: () => _deleteCharacter(characters[index]),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildTabletLayout() {
    return Consumer<CharacterProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final characters = _getFilteredCharacters(provider.characters);

        if (characters.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadCharacters(),
          child: ResponsiveGrid(
            tabletColumns: 2,
            children: characters
                .map(
                  (character) => CharacterCard(
                    character: character,
                    onTap: () => _openCharacter(character),
                    onEdit: () => _editCharacter(character),
                    onDelete: () => _deleteCharacter(character),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildDesktopLayout() {
    return Consumer<CharacterProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final characters = _getFilteredCharacters(provider.characters);

        if (characters.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadCharacters(),
          child: ResponsiveGrid(
            desktopColumns: 3,
            children: characters
                .map(
                  (character) => CharacterCard(
                    character: character,
                    onTap: () => _openCharacter(character),
                    onEdit: () => _editCharacter(character),
                    onDelete: () => _deleteCharacter(character),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '还没有角色',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击右上角的 + 按钮创建你的第一个角色',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showCreateDialog,
            icon: const Icon(Icons.add),
            label: const Text('创建角色'),
          ),
        ],
      ),
    );
  }

  List<Character> _getFilteredCharacters(List<Character> characters) {
    var filtered = characters.where((character) {
      // 搜索过滤
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!character.name.toLowerCase().contains(query) &&
            !character.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // 角色类型过滤
      if (_filterType != null && character.type != _filterType) {
        return false;
      }

      return true;
    }).toList();

    // 排序
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'type':
          comparison = a.type.index.compareTo(b.type.index);
          break;
        case 'createdAt':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'updatedAt':
        default:
          comparison = a.updatedAt.compareTo(b.updatedAt);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索角色'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: '输入角色名称或描述',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
              Navigator.of(context).pop();
            },
            child: const Text('清除'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('筛选角色'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('按角色类型筛选：'),
            const SizedBox(height: 16),
            ...CharacterType.values.map(
              (type) => RadioListTile<CharacterType?>(
                title: Text(_getTypeText(type)),
                value: type,
                groupValue: _filterType,
                onChanged: (value) {
                  setState(() {
                    _filterType = value;
                  });
                  Navigator.of(context).pop();
                },
              ),
            ),
            RadioListTile<CharacterType?>(
              title: const Text('全部'),
              value: null,
              groupValue: _filterType,
              onChanged: (value) {
                setState(() {
                  _filterType = value;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('排序方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('按名称'),
              value: 'name',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按角色类型'),
              value: 'role',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按创建时间'),
              value: 'createdAt',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按更新时间'),
              value: 'updatedAt',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            SwitchListTile(
              title: const Text('升序排列'),
              value: _sortAscending,
              onChanged: (value) {
                setState(() {
                  _sortAscending = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => const CharacterCreateDialog(),
    );
  }

  void _openCharacter(Character character) {
    // TODO: 导航到角色详情页面
    Navigator.of(context).pushNamed('/wiki/characters/${character.id}');
  }

  void _editCharacter(Character character) {
    showDialog(
      context: context,
      builder: (context) => CharacterCreateDialog(character: character),
    );
  }

  void _deleteCharacter(Character character) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除角色'),
        content: Text('确定要删除角色《${character.name}》吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              context.read<CharacterProvider>().deleteCharacter(character.id);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  String _getTypeText(CharacterType type) {
    switch (type) {
      case CharacterType.protagonist:
        return '主角';
      case CharacterType.antagonist:
        return '反派';
      case CharacterType.supporting:
        return '配角';
      case CharacterType.background:
        return '背景角色';
    }
  }
}
