import 'package:flutter/material.dart';
import '../../../../core/widgets/responsive_layout.dart';

import '../widgets/wiki_category_card.dart';

/// Wiki系统主页面
class WikiPage extends StatefulWidget {
  const WikiPage({super.key});

  @override
  State<WikiPage> createState() => _WikiPageState();
}

class _WikiPageState extends State<WikiPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Wiki 百科'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(icon: const Icon(Icons.add), onPressed: _showCreateDialog),
        ],
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 24),
          _buildCategoriesGrid(1),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 32),
          _buildCategoriesGrid(2),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 40),
          _buildCategoriesGrid(3),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.1),
            Theme.of(context).colorScheme.secondary.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_stories,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Text(
                'Wiki 百科',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '构建你的小说世界观，管理角色、设定、时间线等创作要素',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesGrid(int crossAxisCount) {
    final categories = _getWikiCategories();

    return ResponsiveGrid(
      mobileColumns: 1,
      tabletColumns: 2,
      desktopColumns: 3,
      children: categories
          .map(
            (category) => WikiCategoryCard(
              category: category,
              onTap: () => _navigateToCategory(category),
            ),
          )
          .toList(),
    );
  }

  List<WikiCategory> _getWikiCategories() {
    return [
      WikiCategory(
        id: 'characters',
        title: '角色管理',
        description: '创建和管理小说中的角色，包括主角、配角、反派等',
        icon: Icons.people,
        color: Colors.blue,
        count: 0, // TODO: 从数据库获取实际数量
        route: '/wiki/characters',
      ),
      WikiCategory(
        id: 'world-settings',
        title: '世界观设定',
        description: '构建小说的世界观，包括地理、文化、制度等设定',
        icon: Icons.public,
        color: Colors.green,
        count: 0,
        route: '/wiki/world-settings',
      ),
      WikiCategory(
        id: 'timeline',
        title: '时间线',
        description: '管理故事的时间线和重要事件',
        icon: Icons.timeline,
        color: Colors.orange,
        count: 0,
        route: '/wiki/timeline',
      ),
      WikiCategory(
        id: 'items',
        title: '物品道具',
        description: '记录小说中的重要物品、道具、装备等',
        icon: Icons.inventory,
        color: Colors.purple,
        count: 0,
        route: '/wiki/items',
      ),
      WikiCategory(
        id: 'locations',
        title: '地点场景',
        description: '管理故事发生的地点和场景描述',
        icon: Icons.location_on,
        color: Colors.red,
        count: 0,
        route: '/wiki/locations',
      ),
      WikiCategory(
        id: 'organizations',
        title: '组织势力',
        description: '记录小说中的组织、势力、团体等',
        icon: Icons.group_work,
        color: Colors.teal,
        count: 0,
        route: '/wiki/organizations',
      ),
    ];
  }

  void _navigateToCategory(WikiCategory category) {
    Navigator.of(context).pushNamed(category.route);
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索Wiki'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: '输入关键词搜索...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现搜索功能
              Navigator.of(context).pop();
            },
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建Wiki条目'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('选择要创建的Wiki条目类型：'),
            const SizedBox(height: 16),
            ..._getWikiCategories().map(
              (category) => ListTile(
                leading: Icon(category.icon, color: category.color),
                title: Text(category.title),
                subtitle: Text(category.description),
                onTap: () {
                  Navigator.of(context).pop();
                  _navigateToCategory(category);
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }
}

/// Wiki分类数据模型
class WikiCategory {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int count;
  final String route;

  const WikiCategory({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.count,
    required this.route,
  });
}
