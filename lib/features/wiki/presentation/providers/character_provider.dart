import 'package:flutter/foundation.dart';
import '../../domain/entities/character.dart';

/// 角色管理Provider
class CharacterProvider extends ChangeNotifier {
  List<Character> _characters = [];
  bool _isLoading = false;
  String? _error;

  List<Character> get characters => _characters;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// 加载角色列表
  Future<void> loadCharacters() async {
    _setLoading(true);
    try {
      // TODO: 从数据库加载角色
      _characters = [];
      _setError(null);
    } catch (e) {
      _setError('加载角色失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 创建角色
  Future<bool> createCharacter({
    required String name,
    required String description,
    required CharacterType type,
    required CharacterImportance importance,
    int? age,
    String? gender,
    String? appearance,
    String? personality,
    String? background,
    List<String> skills = const [],
    List<String> tags = const [],
  }) async {
    try {
      // TODO: 实现角色创建逻辑
      final character = Character(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        novelId: 'default', // TODO: 从当前选择的小说获取
        name: name,
        aliases: [],
        description: description,
        type: type,
        importance: importance,
        basicInfo: CharacterBasicInfo(age: age, gender: gender),
        appearance: CharacterAppearance(specialMarks: []),
        personality: CharacterPersonality(
          traits: personality != null ? [personality] : [],
          hobbies: [],
          habits: [],
          catchphrases: [],
          fears: [],
        ),
        backstory: background,
        relationships: [],
        skills: skills,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _characters.insert(0, character);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('创建角色失败: $e');
      return false;
    }
  }

  /// 更新角色
  Future<bool> updateCharacter(Character character) async {
    try {
      // TODO: 实现角色更新逻辑
      final index = _characters.indexWhere((c) => c.id == character.id);
      if (index != -1) {
        _characters[index] = character;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('更新角色失败: $e');
      return false;
    }
  }

  /// 删除角色
  Future<bool> deleteCharacter(String id) async {
    try {
      // TODO: 实现角色删除逻辑
      _characters.removeWhere((character) => character.id == id);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('删除角色失败: $e');
      return false;
    }
  }

  /// 搜索角色
  List<Character> searchCharacters(String query) {
    if (query.isEmpty) return _characters;

    return _characters.where((character) {
      return character.name.toLowerCase().contains(query.toLowerCase()) ||
          character.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// 按角色类型筛选
  List<Character> filterByType(CharacterType type) {
    return _characters.where((character) => character.type == type).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}
