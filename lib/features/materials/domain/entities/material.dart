import 'package:json_annotation/json_annotation.dart';

part 'material.g.dart';

/// 素材实体类
@JsonSerializable()
class Material {
  /// 唯一标识符
  final String id;
  
  /// 所属小说ID（可选，全局素材可以为null）
  final String? novelId;
  
  /// 素材标题
  final String title;
  
  /// 素材内容
  final String content;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后修改时间
  final DateTime updatedAt;
  
  /// 素材类型
  final MaterialType type;
  
  /// 素材分类
  final String category;
  
  /// 标签列表
  final List<String> tags;
  
  /// 素材来源
  final String? source;
  
  /// 相关文件路径（图片、音频等）
  final List<String> attachmentPaths;
  
  /// 是否收藏
  final bool isFavorite;
  
  /// 是否已使用
  final bool isUsed;
  
  /// 使用位置（章节ID等）
  final List<String> usedInChapters;
  
  /// 优先级
  final MaterialPriority priority;
  
  /// 备注
  final String? notes;

  const Material({
    required this.id,
    this.novelId,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
    required this.type,
    required this.category,
    required this.tags,
    this.source,
    required this.attachmentPaths,
    required this.isFavorite,
    required this.isUsed,
    required this.usedInChapters,
    required this.priority,
    this.notes,
  });

  /// 从JSON创建Material实例
  factory Material.fromJson(Map<String, dynamic> json) => _$MaterialFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$MaterialToJson(this);

  /// 复制并修改部分属性
  Material copyWith({
    String? id,
    String? novelId,
    String? title,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    MaterialType? type,
    String? category,
    List<String>? tags,
    String? source,
    List<String>? attachmentPaths,
    bool? isFavorite,
    bool? isUsed,
    List<String>? usedInChapters,
    MaterialPriority? priority,
    String? notes,
  }) {
    return Material(
      id: id ?? this.id,
      novelId: novelId ?? this.novelId,
      title: title ?? this.title,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      source: source ?? this.source,
      attachmentPaths: attachmentPaths ?? this.attachmentPaths,
      isFavorite: isFavorite ?? this.isFavorite,
      isUsed: isUsed ?? this.isUsed,
      usedInChapters: usedInChapters ?? this.usedInChapters,
      priority: priority ?? this.priority,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Material && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Material(id: $id, title: $title, type: $type)';
}

/// 素材类型枚举
enum MaterialType {
  /// 灵感
  inspiration,
  /// 想法
  idea,
  /// 情节片段
  plotFragment,
  /// 对话
  dialogue,
  /// 场景描述
  sceneDescription,
  /// 人物描述
  characterDescription,
  /// 环境描述
  environmentDescription,
  /// 研究资料
  research,
  /// 参考图片
  image,
  /// 音频资料
  audio,
  /// 网页链接
  webLink,
  /// 引用文本
  quote,
  /// 其他
  other,
}

/// 素材优先级枚举
enum MaterialPriority {
  /// 高优先级
  high,
  /// 中优先级
  medium,
  /// 低优先级
  low,
}

/// 素材搜索过滤器
class MaterialFilter {
  /// 素材类型过滤
  final List<MaterialType>? types;
  
  /// 分类过滤
  final List<String>? categories;
  
  /// 标签过滤
  final List<String>? tags;
  
  /// 是否收藏过滤
  final bool? isFavorite;
  
  /// 是否已使用过滤
  final bool? isUsed;
  
  /// 优先级过滤
  final List<MaterialPriority>? priorities;
  
  /// 创建时间范围过滤
  final DateRange? createdDateRange;
  
  /// 更新时间范围过滤
  final DateRange? updatedDateRange;
  
  /// 关键词搜索
  final String? keyword;

  const MaterialFilter({
    this.types,
    this.categories,
    this.tags,
    this.isFavorite,
    this.isUsed,
    this.priorities,
    this.createdDateRange,
    this.updatedDateRange,
    this.keyword,
  });

  /// 复制并修改部分属性
  MaterialFilter copyWith({
    List<MaterialType>? types,
    List<String>? categories,
    List<String>? tags,
    bool? isFavorite,
    bool? isUsed,
    List<MaterialPriority>? priorities,
    DateRange? createdDateRange,
    DateRange? updatedDateRange,
    String? keyword,
  }) {
    return MaterialFilter(
      types: types ?? this.types,
      categories: categories ?? this.categories,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      isUsed: isUsed ?? this.isUsed,
      priorities: priorities ?? this.priorities,
      createdDateRange: createdDateRange ?? this.createdDateRange,
      updatedDateRange: updatedDateRange ?? this.updatedDateRange,
      keyword: keyword ?? this.keyword,
    );
  }
}

/// 日期范围
class DateRange {
  /// 开始日期
  final DateTime start;
  
  /// 结束日期
  final DateTime end;

  const DateRange({
    required this.start,
    required this.end,
  });

  /// 检查日期是否在范围内
  bool contains(DateTime date) {
    return date.isAfter(start.subtract(const Duration(days: 1))) &&
           date.isBefore(end.add(const Duration(days: 1)));
  }

  @override
  String toString() => '${start.toString().split(' ')[0]} - ${end.toString().split(' ')[0]}';
}
