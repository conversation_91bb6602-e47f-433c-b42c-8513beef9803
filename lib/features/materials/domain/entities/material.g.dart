// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'material.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Material _$MaterialFromJson(Map<String, dynamic> json) => Material(
  id: json['id'] as String,
  novelId: json['novelId'] as String?,
  title: json['title'] as String,
  content: json['content'] as String,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  type: $enumDecode(_$MaterialTypeEnumMap, json['type']),
  category: json['category'] as String,
  tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
  source: json['source'] as String?,
  attachmentPaths: (json['attachmentPaths'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  isFavorite: json['isFavorite'] as bool,
  isUsed: json['isUsed'] as bool,
  usedInChapters: (json['usedInChapters'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  priority: $enumDecode(_$MaterialPriorityEnumMap, json['priority']),
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$MaterialToJson(Material instance) => <String, dynamic>{
  'id': instance.id,
  'novelId': instance.novelId,
  'title': instance.title,
  'content': instance.content,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'type': _$MaterialTypeEnumMap[instance.type]!,
  'category': instance.category,
  'tags': instance.tags,
  'source': instance.source,
  'attachmentPaths': instance.attachmentPaths,
  'isFavorite': instance.isFavorite,
  'isUsed': instance.isUsed,
  'usedInChapters': instance.usedInChapters,
  'priority': _$MaterialPriorityEnumMap[instance.priority]!,
  'notes': instance.notes,
};

const _$MaterialTypeEnumMap = {
  MaterialType.inspiration: 'inspiration',
  MaterialType.idea: 'idea',
  MaterialType.plotFragment: 'plotFragment',
  MaterialType.dialogue: 'dialogue',
  MaterialType.sceneDescription: 'sceneDescription',
  MaterialType.characterDescription: 'characterDescription',
  MaterialType.environmentDescription: 'environmentDescription',
  MaterialType.research: 'research',
  MaterialType.image: 'image',
  MaterialType.audio: 'audio',
  MaterialType.webLink: 'webLink',
  MaterialType.quote: 'quote',
  MaterialType.other: 'other',
};

const _$MaterialPriorityEnumMap = {
  MaterialPriority.high: 'high',
  MaterialPriority.medium: 'medium',
  MaterialPriority.low: 'low',
};
