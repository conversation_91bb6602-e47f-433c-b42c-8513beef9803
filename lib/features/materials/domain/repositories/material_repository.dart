import '../entities/material.dart';

/// 素材仓库接口
abstract class MaterialRepository {
  /// 创建素材
  Future<void> createMaterial(Material material);
  
  /// 更新素材
  Future<void> updateMaterial(Material material);
  
  /// 删除素材
  Future<void> deleteMaterial(String id);
  
  /// 根据ID获取素材
  Future<Material?> getMaterialById(String id);
  
  /// 获取所有素材
  Future<List<Material>> getAllMaterials();
  
  /// 根据小说ID获取素材
  Future<List<Material>> getMaterialsByNovelId(String? novelId);
  
  /// 根据类型获取素材
  Future<List<Material>> getMaterialsByType(MaterialType type);
  
  /// 根据分类获取素材
  Future<List<Material>> getMaterialsByCategory(String category);
  
  /// 获取收藏的素材
  Future<List<Material>> getFavoriteMaterials();
  
  /// 搜索素材
  Future<List<Material>> searchMaterials(String keyword);
  
  /// 根据过滤器获取素材
  Future<List<Material>> getMaterialsWithFilter(MaterialFilter filter);
  
  /// 获取素材统计信息
  Future<Map<String, dynamic>> getMaterialStatistics();
  
  /// 获取所有分类
  Future<List<String>> getAllCategories();
  
  /// 获取所有标签
  Future<List<String>> getAllTags();
}
