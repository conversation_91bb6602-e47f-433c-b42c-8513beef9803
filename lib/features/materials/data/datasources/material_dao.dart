import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_helper.dart';
import '../../domain/entities/material.dart';

/// 素材数据访问对象
class MaterialDao {
  final DatabaseHelper _databaseHelper;

  MaterialDao(this._databaseHelper);

  /// 获取数据库实例
  Future<Database> get _database => _databaseHelper.database;

  /// 插入素材
  Future<void> insertMaterial(Material material) async {
    final db = await _database;
    await db.insert(
      'materials',
      _materialToMap(material),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// 更新素材
  Future<void> updateMaterial(Material material) async {
    final db = await _database;
    await db.update(
      'materials',
      _materialToMap(material),
      where: 'id = ?',
      whereArgs: [material.id],
    );
  }

  /// 删除素材
  Future<void> deleteMaterial(String id) async {
    final db = await _database;
    await db.delete('materials', where: 'id = ?', whereArgs: [id]);
  }

  /// 根据ID获取素材
  Future<Material?> getMaterialById(String id) async {
    final db = await _database;
    final maps = await db.query('materials', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return _mapToMaterial(maps.first);
    }
    return null;
  }

  /// 获取所有素材
  Future<List<Material>> getAllMaterials() async {
    final db = await _database;
    final maps = await db.query('materials', orderBy: 'updated_at DESC');

    return maps.map((map) => _mapToMaterial(map)).toList();
  }

  /// 根据小说ID获取素材
  Future<List<Material>> getMaterialsByNovelId(String? novelId) async {
    final db = await _database;
    final maps = await db.query(
      'materials',
      where: novelId != null ? 'novel_id = ?' : 'novel_id IS NULL',
      whereArgs: novelId != null ? [novelId] : null,
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToMaterial(map)).toList();
  }

  /// 根据类型获取素材
  Future<List<Material>> getMaterialsByType(MaterialType type) async {
    final db = await _database;
    final maps = await db.query(
      'materials',
      where: 'type = ?',
      whereArgs: [type.name],
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToMaterial(map)).toList();
  }

  /// 根据分类获取素材
  Future<List<Material>> getMaterialsByCategory(String category) async {
    final db = await _database;
    final maps = await db.query(
      'materials',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToMaterial(map)).toList();
  }

  /// 获取收藏的素材
  Future<List<Material>> getFavoriteMaterials() async {
    final db = await _database;
    final maps = await db.query(
      'materials',
      where: 'is_favorite = 1',
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToMaterial(map)).toList();
  }

  /// 搜索素材
  Future<List<Material>> searchMaterials(String keyword) async {
    final db = await _database;
    final maps = await db.query(
      'materials',
      where: 'title LIKE ? OR content LIKE ? OR tags LIKE ?',
      whereArgs: ['%$keyword%', '%$keyword%', '%$keyword%'],
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToMaterial(map)).toList();
  }

  /// 根据过滤器获取素材
  Future<List<Material>> getMaterialsWithFilter(MaterialFilter filter) async {
    final db = await _database;
    final whereConditions = <String>[];
    final whereArgs = <dynamic>[];

    // 类型过滤
    if (filter.types != null && filter.types!.isNotEmpty) {
      final typeNames = filter.types!.map((type) => type.name).toList();
      whereConditions.add('type IN (${typeNames.map((_) => '?').join(', ')})');
      whereArgs.addAll(typeNames);
    }

    // 分类过滤
    if (filter.categories != null && filter.categories!.isNotEmpty) {
      whereConditions.add(
        'category IN (${filter.categories!.map((_) => '?').join(', ')})',
      );
      whereArgs.addAll(filter.categories!);
    }

    // 收藏过滤
    if (filter.isFavorite != null) {
      whereConditions.add('is_favorite = ?');
      whereArgs.add(filter.isFavorite! ? 1 : 0);
    }

    // 使用状态过滤
    if (filter.isUsed != null) {
      whereConditions.add('is_used = ?');
      whereArgs.add(filter.isUsed! ? 1 : 0);
    }

    // 优先级过滤
    if (filter.priorities != null && filter.priorities!.isNotEmpty) {
      final priorityNames = filter.priorities!
          .map((priority) => priority.name)
          .toList();
      whereConditions.add(
        'priority IN (${priorityNames.map((_) => '?').join(', ')})',
      );
      whereArgs.addAll(priorityNames);
    }

    // 关键词搜索
    if (filter.keyword != null && filter.keyword!.isNotEmpty) {
      whereConditions.add('(title LIKE ? OR content LIKE ? OR tags LIKE ?)');
      final keyword = '%${filter.keyword}%';
      whereArgs.addAll([keyword, keyword, keyword]);
    }

    // 创建时间范围过滤
    if (filter.createdDateRange != null) {
      whereConditions.add('created_at >= ? AND created_at <= ?');
      whereArgs.add(filter.createdDateRange!.start.millisecondsSinceEpoch);
      whereArgs.add(filter.createdDateRange!.end.millisecondsSinceEpoch);
    }

    final whereClause = whereConditions.isNotEmpty
        ? whereConditions.join(' AND ')
        : null;

    final maps = await db.query(
      'materials',
      where: whereClause,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToMaterial(map)).toList();
  }

  /// 获取素材统计信息
  Future<Map<String, dynamic>> getMaterialStatistics() async {
    final db = await _database;

    // 总素材数
    final totalCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM materials'),
        ) ??
        0;

    // 各类型素材数
    final typeCounts = <String, int>{};
    for (final type in MaterialType.values) {
      final count =
          Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM materials WHERE type = ?', [
              type.name,
            ]),
          ) ??
          0;
      typeCounts[type.name] = count;
    }

    // 收藏数
    final favoriteCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM materials WHERE is_favorite = 1',
          ),
        ) ??
        0;

    // 已使用数
    final usedCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM materials WHERE is_used = 1'),
        ) ??
        0;

    return {
      'totalCount': totalCount,
      'typeCounts': typeCounts,
      'favoriteCount': favoriteCount,
      'usedCount': usedCount,
    };
  }

  /// 获取所有分类
  Future<List<String>> getAllCategories() async {
    final db = await _database;
    final maps = await db.rawQuery(
      'SELECT DISTINCT category FROM materials WHERE category IS NOT NULL ORDER BY category',
    );

    return maps.map((map) => map['category'] as String).toList();
  }

  /// 获取所有标签
  Future<List<String>> getAllTags() async {
    final db = await _database;
    final maps = await db.rawQuery(
      'SELECT tags FROM materials WHERE tags IS NOT NULL',
    );

    final allTags = <String>{};
    for (final map in maps) {
      final tags = List<String>.from(jsonDecode(map['tags'] as String));
      allTags.addAll(tags);
    }

    return allTags.toList()..sort();
  }

  /// 将Material对象转换为Map
  Map<String, dynamic> _materialToMap(Material material) {
    return {
      'id': material.id,
      'novel_id': material.novelId,
      'title': material.title,
      'content': material.content,
      'created_at': material.createdAt.millisecondsSinceEpoch,
      'updated_at': material.updatedAt.millisecondsSinceEpoch,
      'type': material.type.name,
      'category': material.category,
      'tags': jsonEncode(material.tags),
      'source': material.source,
      'attachment_paths': jsonEncode(material.attachmentPaths),
      'is_favorite': material.isFavorite ? 1 : 0,
      'is_used': material.isUsed ? 1 : 0,
      'used_in_chapters': jsonEncode(material.usedInChapters),
      'priority': material.priority.name,
      'notes': material.notes,
    };
  }

  /// 将Map转换为Material对象
  Material _mapToMaterial(Map<String, dynamic> map) {
    return Material(
      id: map['id'] as String,
      novelId: map['novel_id'] as String?,
      title: map['title'] as String,
      content: map['content'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      type: MaterialType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => MaterialType.other,
      ),
      category: map['category'] as String,
      tags: List<String>.from(jsonDecode(map['tags'] as String)),
      source: map['source'] as String?,
      attachmentPaths: List<String>.from(
        jsonDecode(map['attachment_paths'] as String),
      ),
      isFavorite: (map['is_favorite'] as int) == 1,
      isUsed: (map['is_used'] as int) == 1,
      usedInChapters: List<String>.from(
        jsonDecode(map['used_in_chapters'] as String),
      ),
      priority: MaterialPriority.values.firstWhere(
        (priority) => priority.name == map['priority'],
        orElse: () => MaterialPriority.medium,
      ),
      notes: map['notes'] as String?,
    );
  }
}
