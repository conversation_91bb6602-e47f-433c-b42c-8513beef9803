import '../../domain/entities/material.dart';
import '../../domain/repositories/material_repository.dart';
import '../datasources/material_dao.dart';

/// 素材仓库实现
class MaterialRepositoryImpl implements MaterialRepository {
  final MaterialDao _materialDao;

  MaterialRepositoryImpl({required MaterialDao materialDao}) : _materialDao = materialDao;

  @override
  Future<void> createMaterial(Material material) async {
    await _materialDao.insertMaterial(material);
  }

  @override
  Future<void> updateMaterial(Material material) async {
    await _materialDao.updateMaterial(material);
  }

  @override
  Future<void> deleteMaterial(String id) async {
    await _materialDao.deleteMaterial(id);
  }

  @override
  Future<Material?> getMaterialById(String id) async {
    return await _materialDao.getMaterialById(id);
  }

  @override
  Future<List<Material>> getAllMaterials() async {
    return await _materialDao.getAllMaterials();
  }

  @override
  Future<List<Material>> getMaterialsByNovelId(String? novelId) async {
    return await _materialDao.getMaterialsByNovelId(novelId);
  }

  @override
  Future<List<Material>> getMaterialsByType(MaterialType type) async {
    return await _materialDao.getMaterialsByType(type);
  }

  @override
  Future<List<Material>> getMaterialsByCategory(String category) async {
    return await _materialDao.getMaterialsByCategory(category);
  }

  @override
  Future<List<Material>> getFavoriteMaterials() async {
    return await _materialDao.getFavoriteMaterials();
  }

  @override
  Future<List<Material>> searchMaterials(String keyword) async {
    return await _materialDao.searchMaterials(keyword);
  }

  @override
  Future<List<Material>> getMaterialsWithFilter(MaterialFilter filter) async {
    return await _materialDao.getMaterialsWithFilter(filter);
  }

  @override
  Future<Map<String, dynamic>> getMaterialStatistics() async {
    return await _materialDao.getMaterialStatistics();
  }

  @override
  Future<List<String>> getAllCategories() async {
    return await _materialDao.getAllCategories();
  }

  @override
  Future<List<String>> getAllTags() async {
    return await _materialDao.getAllTags();
  }
}
