import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/material.dart';
import '../../domain/repositories/material_repository.dart';

/// 素材状态管理
class MaterialProvider extends ChangeNotifier {
  final MaterialRepository _repository;
  final Uuid _uuid = const Uuid();

  MaterialProvider({required MaterialRepository repository}) : _repository = repository;

  // 状态变量
  List<Material> _materials = [];
  List<Material> _filteredMaterials = [];
  Material? _currentMaterial;
  bool _isLoading = false;
  String? _error;
  MaterialFilter _currentFilter = const MaterialFilter();
  Map<String, dynamic>? _statistics;
  List<String> _categories = [];
  List<String> _tags = [];

  // Getters
  List<Material> get materials => _filteredMaterials.isNotEmpty ? _filteredMaterials : _materials;
  Material? get currentMaterial => _currentMaterial;
  bool get isLoading => _isLoading;
  String? get error => _error;
  MaterialFilter get currentFilter => _currentFilter;
  Map<String, dynamic>? get statistics => _statistics;
  List<String> get categories => _categories;
  List<String> get tags => _tags;

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 加载所有素材
  Future<void> loadMaterials() async {
    try {
      _setLoading(true);
      _setError(null);
      _materials = await _repository.getAllMaterials();
      _filteredMaterials.clear();
      notifyListeners();
    } catch (e) {
      _setError('加载素材列表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 根据小说ID加载素材
  Future<void> loadMaterialsByNovelId(String? novelId) async {
    try {
      _setLoading(true);
      _setError(null);
      _materials = await _repository.getMaterialsByNovelId(novelId);
      _filteredMaterials.clear();
      notifyListeners();
    } catch (e) {
      _setError('加载素材列表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 创建新素材
  Future<bool> createMaterial({
    String? novelId,
    required String title,
    required String content,
    required MaterialType type,
    required String category,
    List<String> tags = const [],
    String? source,
    List<String> attachmentPaths = const [],
    MaterialPriority priority = MaterialPriority.medium,
    String? notes,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final material = Material(
        id: _uuid.v4(),
        novelId: novelId,
        title: title,
        content: content,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        type: type,
        category: category,
        tags: tags,
        source: source,
        attachmentPaths: attachmentPaths,
        isFavorite: false,
        isUsed: false,
        usedInChapters: [],
        priority: priority,
        notes: notes,
      );

      await _repository.createMaterial(material);
      _materials.insert(0, material);
      
      // 更新分类和标签列表
      await _loadCategoriesAndTags();
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('创建素材失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 更新素材
  Future<bool> updateMaterial(Material material) async {
    try {
      _setLoading(true);
      _setError(null);

      final updatedMaterial = material.copyWith(updatedAt: DateTime.now());
      await _repository.updateMaterial(updatedMaterial);

      final index = _materials.indexWhere((m) => m.id == material.id);
      if (index != -1) {
        _materials[index] = updatedMaterial;
      }

      if (_currentMaterial?.id == material.id) {
        _currentMaterial = updatedMaterial;
      }

      // 更新分类和标签列表
      await _loadCategoriesAndTags();
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('更新素材失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 删除素材
  Future<bool> deleteMaterial(String id) async {
    try {
      _setLoading(true);
      _setError(null);

      await _repository.deleteMaterial(id);
      _materials.removeWhere((material) => material.id == id);

      if (_currentMaterial?.id == id) {
        _currentMaterial = null;
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError('删除素材失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 设置当前素材
  void setCurrentMaterial(Material material) {
    _currentMaterial = material;
    notifyListeners();
  }

  /// 切换收藏状态
  Future<bool> toggleFavorite(String materialId) async {
    try {
      final material = _materials.firstWhere((m) => m.id == materialId);
      final updatedMaterial = material.copyWith(
        isFavorite: !material.isFavorite,
        updatedAt: DateTime.now(),
      );
      
      return await updateMaterial(updatedMaterial);
    } catch (e) {
      _setError('切换收藏状态失败: $e');
      return false;
    }
  }

  /// 标记为已使用
  Future<bool> markAsUsed(String materialId, String chapterId) async {
    try {
      final material = _materials.firstWhere((m) => m.id == materialId);
      final usedInChapters = List<String>.from(material.usedInChapters);
      
      if (!usedInChapters.contains(chapterId)) {
        usedInChapters.add(chapterId);
      }
      
      final updatedMaterial = material.copyWith(
        isUsed: true,
        usedInChapters: usedInChapters,
        updatedAt: DateTime.now(),
      );
      
      return await updateMaterial(updatedMaterial);
    } catch (e) {
      _setError('标记使用状态失败: $e');
      return false;
    }
  }

  /// 应用过滤器
  Future<void> applyFilter(MaterialFilter filter) async {
    try {
      _setLoading(true);
      _setError(null);
      
      _currentFilter = filter;
      _filteredMaterials = await _repository.getMaterialsWithFilter(filter);
      notifyListeners();
    } catch (e) {
      _setError('应用过滤器失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 清除过滤器
  void clearFilter() {
    _currentFilter = const MaterialFilter();
    _filteredMaterials.clear();
    notifyListeners();
  }

  /// 搜索素材
  Future<void> searchMaterials(String keyword) async {
    try {
      _setLoading(true);
      _setError(null);

      if (keyword.isEmpty) {
        await loadMaterials();
      } else {
        _materials = await _repository.searchMaterials(keyword);
        _filteredMaterials.clear();
        notifyListeners();
      }
    } catch (e) {
      _setError('搜索素材失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 按类型过滤
  Future<void> filterByType(MaterialType type) async {
    final filter = _currentFilter.copyWith(types: [type]);
    await applyFilter(filter);
  }

  /// 按分类过滤
  Future<void> filterByCategory(String category) async {
    final filter = _currentFilter.copyWith(categories: [category]);
    await applyFilter(filter);
  }

  /// 只显示收藏
  Future<void> showFavoritesOnly() async {
    final filter = _currentFilter.copyWith(isFavorite: true);
    await applyFilter(filter);
  }

  /// 只显示未使用
  Future<void> showUnusedOnly() async {
    final filter = _currentFilter.copyWith(isUsed: false);
    await applyFilter(filter);
  }

  /// 加载统计信息
  Future<void> loadStatistics() async {
    try {
      _setError(null);
      _statistics = await _repository.getMaterialStatistics();
      notifyListeners();
    } catch (e) {
      _setError('加载统计信息失败: $e');
    }
  }

  /// 加载分类和标签
  Future<void> _loadCategoriesAndTags() async {
    try {
      _categories = await _repository.getAllCategories();
      _tags = await _repository.getAllTags();
    } catch (e) {
      // 静默处理错误，不影响主要功能
      debugPrint('加载分类和标签失败: $e');
    }
  }

  /// 获取收藏素材
  List<Material> get favoriteMaterials {
    return materials.where((material) => material.isFavorite).toList();
  }

  /// 获取未使用素材
  List<Material> get unusedMaterials {
    return materials.where((material) => !material.isUsed).toList();
  }

  /// 按类型分组素材
  Map<MaterialType, List<Material>> get materialsByType {
    final grouped = <MaterialType, List<Material>>{};
    for (final material in materials) {
      grouped.putIfAbsent(material.type, () => []).add(material);
    }
    return grouped;
  }

  /// 按分类分组素材
  Map<String, List<Material>> get materialsByCategory {
    final grouped = <String, List<Material>>{};
    for (final material in materials) {
      grouped.putIfAbsent(material.category, () => []).add(material);
    }
    return grouped;
  }

  /// 获取最近创建的素材
  List<Material> getRecentMaterials({int limit = 10}) {
    final sortedMaterials = List<Material>.from(materials)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedMaterials.take(limit).toList();
  }

  /// 获取最近修改的素材
  List<Material> getRecentlyUpdatedMaterials({int limit = 10}) {
    final sortedMaterials = List<Material>.from(materials)
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    return sortedMaterials.take(limit).toList();
  }
}
