import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/material.dart' as material_entity;

/// 素材卡片组件
class MaterialCard extends StatelessWidget {
  final material_entity.Material material;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const MaterialCard({
    super.key,
    required this.material,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部：标题和菜单
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          material.title,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getTypeColor(material.type),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _getTypeText(material.type),
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('编辑'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text(
                            '删除',
                            style: TextStyle(color: Colors.red),
                          ),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 内容预览
              if (material.content.isNotEmpty) ...[
                Text(
                  material.content,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
              ],

              // 标签
              if (material.tags.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: material.tags
                      .take(3)
                      .map(
                        (tag) => Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            tag,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                                ),
                          ),
                        ),
                      )
                      .toList(),
                ),
                const SizedBox(height: 12),
              ],

              // 底部：时间和状态
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.5),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('MM/dd HH:mm').format(material.updatedAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ),
                  const Spacer(),
                  if (material.isFavorite) ...[
                    Icon(Icons.favorite, size: 14, color: Colors.red),
                    const SizedBox(width: 8),
                  ],
                  if (material.attachmentPaths.isNotEmpty) ...[
                    Icon(
                      Icons.attach_file,
                      size: 14,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.5),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${material.attachmentPaths.length}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTypeText(material_entity.MaterialType type) {
    switch (type) {
      case material_entity.MaterialType.inspiration:
        return '灵感';
      case material_entity.MaterialType.idea:
        return '想法';
      case material_entity.MaterialType.plotFragment:
        return '情节片段';
      case material_entity.MaterialType.dialogue:
        return '对话';
      case material_entity.MaterialType.sceneDescription:
        return '场景描述';
      case material_entity.MaterialType.characterDescription:
        return '人物描述';
      case material_entity.MaterialType.environmentDescription:
        return '环境描述';
      case material_entity.MaterialType.research:
        return '研究资料';
      case material_entity.MaterialType.image:
        return '参考图片';
      case material_entity.MaterialType.audio:
        return '音频资料';
      case material_entity.MaterialType.webLink:
        return '网页链接';
      case material_entity.MaterialType.quote:
        return '引用文本';
      case material_entity.MaterialType.other:
        return '其他';
    }
  }

  Color _getTypeColor(material_entity.MaterialType type) {
    switch (type) {
      case material_entity.MaterialType.inspiration:
        return Colors.amber;
      case material_entity.MaterialType.idea:
        return Colors.lightBlue;
      case material_entity.MaterialType.plotFragment:
        return Colors.green;
      case material_entity.MaterialType.dialogue:
        return Colors.purple;
      case material_entity.MaterialType.sceneDescription:
        return Colors.orange;
      case material_entity.MaterialType.characterDescription:
        return Colors.pink;
      case material_entity.MaterialType.environmentDescription:
        return Colors.teal;
      case material_entity.MaterialType.research:
        return Colors.indigo;
      case material_entity.MaterialType.image:
        return Colors.red;
      case material_entity.MaterialType.audio:
        return Colors.deepOrange;
      case material_entity.MaterialType.webLink:
        return Colors.cyan;
      case material_entity.MaterialType.quote:
        return Colors.brown;
      case material_entity.MaterialType.other:
        return Colors.grey;
    }
  }
}
