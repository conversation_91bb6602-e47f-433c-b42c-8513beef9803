import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../domain/entities/material.dart' as material_entity;
import '../providers/material_provider.dart';

/// 素材创建/编辑对话框
class MaterialCreateDialog extends StatefulWidget {
  final material_entity.Material? material; // 如果为null则是创建，否则是编辑

  const MaterialCreateDialog({super.key, this.material});

  @override
  State<MaterialCreateDialog> createState() => _MaterialCreateDialogState();
}

class _MaterialCreateDialogState extends State<MaterialCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagsController = TextEditingController();

  material_entity.MaterialType _type = material_entity.MaterialType.inspiration;
  bool _isFavorite = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.material != null) {
      _titleController.text = widget.material!.title;
      _contentController.text = widget.material!.content;
      _tagsController.text = widget.material!.tags.join(', ');
      _type = widget.material!.type;
      _isFavorite = widget.material!.isFavorite;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.material != null;

    return AlertDialog(
      title: Text(isEditing ? '编辑素材' : '创建新素材'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.6,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: '标题',
                    hintText: '请输入素材标题',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入标题';
                    }
                    return null;
                  },
                  maxLength: 100,
                ),
                const SizedBox(height: 16),

                // 类型
                DropdownButtonFormField<material_entity.MaterialType>(
                  value: _type,
                  decoration: const InputDecoration(
                    labelText: '素材类型',
                    border: OutlineInputBorder(),
                  ),
                  items: material_entity.MaterialType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _type = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // 内容
                TextFormField(
                  controller: _contentController,
                  decoration: const InputDecoration(
                    labelText: '内容',
                    hintText: '请输入素材内容',
                    border: OutlineInputBorder(),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 8,
                  maxLength: 5000,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入内容';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // 标签
                TextFormField(
                  controller: _tagsController,
                  decoration: const InputDecoration(
                    labelText: '标签',
                    hintText: '用逗号分隔多个标签（可选）',
                    border: OutlineInputBorder(),
                  ),
                  maxLength: 200,
                ),
                const SizedBox(height: 16),

                // 收藏
                SwitchListTile(
                  title: const Text('添加到收藏'),
                  subtitle: const Text('收藏的素材会在收藏夹中显示'),
                  value: _isFavorite,
                  onChanged: (value) {
                    setState(() {
                      _isFavorite = value;
                    });
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveMaterial,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? '保存' : '创建'),
        ),
      ],
    );
  }

  String _getTypeText(material_entity.MaterialType type) {
    switch (type) {
      case material_entity.MaterialType.inspiration:
        return '灵感';
      case material_entity.MaterialType.idea:
        return '想法';
      case material_entity.MaterialType.plotFragment:
        return '情节片段';
      case material_entity.MaterialType.dialogue:
        return '对话';
      case material_entity.MaterialType.sceneDescription:
        return '场景描述';
      case material_entity.MaterialType.characterDescription:
        return '人物描述';
      case material_entity.MaterialType.environmentDescription:
        return '环境描述';
      case material_entity.MaterialType.research:
        return '研究资料';
      case material_entity.MaterialType.image:
        return '参考图片';
      case material_entity.MaterialType.audio:
        return '音频资料';
      case material_entity.MaterialType.webLink:
        return '网页链接';
      case material_entity.MaterialType.quote:
        return '引用文本';
      case material_entity.MaterialType.other:
        return '其他';
    }
  }

  Future<void> _saveMaterial() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final title = _titleController.text.trim();
      final content = _contentController.text.trim();
      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      final provider = context.read<MaterialProvider>();

      if (widget.material != null) {
        // 编辑现有素材
        final updatedMaterial = widget.material!.copyWith(
          title: title,
          content: content,
          type: _type,
          tags: tags,
          isFavorite: _isFavorite,
          updatedAt: DateTime.now(),
        );
        await provider.updateMaterial(updatedMaterial);
      } else {
        // 创建新素材
        await provider.createMaterial(
          title: title,
          content: content,
          type: _type,
          category: '默认分类', // TODO: 添加分类选择
          tags: tags,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.material != null ? '素材已更新' : '素材创建成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('操作失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
