import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/theme/app_theme.dart';
import '../providers/material_provider.dart';
import '../widgets/material_card.dart';
import '../widgets/material_create_dialog.dart';

import '../../domain/entities/material.dart' as material_entity;

/// 素材库页面
class MaterialsPage extends StatefulWidget {
  const MaterialsPage({super.key});

  @override
  State<MaterialsPage> createState() => _MaterialsPageState();
}

class _MaterialsPageState extends State<MaterialsPage> {
  String _searchQuery = '';
  material_entity.MaterialType? _filterType;
  String _sortBy = 'updatedAt';
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MaterialProvider>().loadMaterials();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('素材库'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(icon: const Icon(Icons.sort), onPressed: _showSortDialog),
          IconButton(icon: const Icon(Icons.add), onPressed: _showCreateDialog),
        ],
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
      floatingActionButton: AppTheme.isMobile(context)
          ? FloatingActionButton(
              onPressed: _showCreateDialog,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildMobileLayout() {
    return Consumer<MaterialProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final materials = _getFilteredMaterials(provider.materials);

        if (materials.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadMaterials(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: materials.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: MaterialCard(
                  material: materials[index],
                  onTap: () => _openMaterial(materials[index]),
                  onEdit: () => _editMaterial(materials[index]),
                  onDelete: () => _deleteMaterial(materials[index]),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildTabletLayout() {
    return Consumer<MaterialProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final materials = _getFilteredMaterials(provider.materials);

        if (materials.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadMaterials(),
          child: ResponsiveGrid(
            tabletColumns: 2,
            children: materials
                .map(
                  (material) => MaterialCard(
                    material: material,
                    onTap: () => _openMaterial(material),
                    onEdit: () => _editMaterial(material),
                    onDelete: () => _deleteMaterial(material),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildDesktopLayout() {
    return Consumer<MaterialProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final materials = _getFilteredMaterials(provider.materials);

        if (materials.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadMaterials(),
          child: ResponsiveGrid(
            desktopColumns: 3,
            children: materials
                .map(
                  (material) => MaterialCard(
                    material: material,
                    onTap: () => _openMaterial(material),
                    onEdit: () => _editMaterial(material),
                    onDelete: () => _deleteMaterial(material),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '还没有素材',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击右上角的 + 按钮记录你的第一个灵感',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showCreateDialog,
            icon: const Icon(Icons.add),
            label: const Text('创建素材'),
          ),
        ],
      ),
    );
  }

  List<material_entity.Material> _getFilteredMaterials(
    List<material_entity.Material> materials,
  ) {
    var filtered = materials.where((material) {
      // 搜索过滤
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!material.title.toLowerCase().contains(query) &&
            !material.content.toLowerCase().contains(query)) {
          return false;
        }
      }

      // 类型过滤
      if (_filterType != null && material.type != _filterType) {
        return false;
      }

      return true;
    }).toList();

    // 排序
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'title':
          comparison = a.title.compareTo(b.title);
          break;
        case 'type':
          comparison = a.type.index.compareTo(b.type.index);
          break;
        case 'createdAt':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'updatedAt':
        default:
          comparison = a.updatedAt.compareTo(b.updatedAt);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索素材'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: '输入标题或内容关键词',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
              Navigator.of(context).pop();
            },
            child: const Text('清除'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('筛选素材'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('按素材类型筛选：'),
            const SizedBox(height: 16),
            ...material_entity.MaterialType.values.map(
              (type) => RadioListTile<material_entity.MaterialType?>(
                title: Text(_getTypeText(type)),
                value: type,
                groupValue: _filterType,
                onChanged: (value) {
                  setState(() {
                    _filterType = value;
                  });
                  Navigator.of(context).pop();
                },
              ),
            ),
            RadioListTile<material_entity.MaterialType?>(
              title: const Text('全部'),
              value: null,
              groupValue: _filterType,
              onChanged: (value) {
                setState(() {
                  _filterType = value;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('排序方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('按标题'),
              value: 'title',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按类型'),
              value: 'type',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按创建时间'),
              value: 'createdAt',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按更新时间'),
              value: 'updatedAt',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            SwitchListTile(
              title: const Text('升序排列'),
              value: _sortAscending,
              onChanged: (value) {
                setState(() {
                  _sortAscending = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => const MaterialCreateDialog(),
    );
  }

  void _openMaterial(material_entity.Material material) {
    // TODO: 导航到素材详情页面
    Navigator.of(context).pushNamed('/materials/${material.id}');
  }

  void _editMaterial(material_entity.Material material) {
    showDialog(
      context: context,
      builder: (context) => MaterialCreateDialog(material: material),
    );
  }

  void _deleteMaterial(material_entity.Material material) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除素材'),
        content: Text('确定要删除素材《${material.title}》吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              context.read<MaterialProvider>().deleteMaterial(material.id);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  String _getTypeText(material_entity.MaterialType type) {
    switch (type) {
      case material_entity.MaterialType.inspiration:
        return '灵感';
      case material_entity.MaterialType.idea:
        return '想法';
      case material_entity.MaterialType.plotFragment:
        return '情节片段';
      case material_entity.MaterialType.dialogue:
        return '对话';
      case material_entity.MaterialType.sceneDescription:
        return '场景描述';
      case material_entity.MaterialType.characterDescription:
        return '人物描述';
      case material_entity.MaterialType.environmentDescription:
        return '环境描述';
      case material_entity.MaterialType.research:
        return '研究资料';
      case material_entity.MaterialType.image:
        return '参考图片';
      case material_entity.MaterialType.audio:
        return '音频资料';
      case material_entity.MaterialType.webLink:
        return '网页链接';
      case material_entity.MaterialType.quote:
        return '引用文本';
      case material_entity.MaterialType.other:
        return '其他';
    }
  }
}
