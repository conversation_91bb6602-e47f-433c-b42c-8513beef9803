import '../../domain/entities/novel.dart';
import '../../domain/entities/chapter.dart';
import '../../domain/repositories/novel_repository.dart';
import '../datasources/novel_dao.dart';
import '../datasources/chapter_dao.dart';

/// 小说仓库实现
class NovelRepositoryImpl implements NovelRepository {
  final NovelDao _novelDao;
  final ChapterDao _chapterDao;

  NovelRepositoryImpl({
    required NovelDao novelDao,
    required ChapterDao chapterDao,
  })  : _novelDao = novelDao,
        _chapterDao = chapterDao;

  // 小说相关操作
  @override
  Future<void> createNovel(Novel novel) async {
    await _novelDao.insertNovel(novel);
  }

  @override
  Future<void> updateNovel(Novel novel) async {
    await _novelDao.updateNovel(novel);
  }

  @override
  Future<void> deleteNovel(String id) async {
    await _novelDao.deleteNovel(id);
  }

  @override
  Future<Novel?> getNovelById(String id) async {
    return await _novelDao.getNovelById(id);
  }

  @override
  Future<List<Novel>> getAllNovels() async {
    return await _novelDao.getAllNovels();
  }

  @override
  Future<List<Novel>> getNovelsByStatus(NovelStatus status) async {
    return await _novelDao.getNovelsByStatus(status);
  }

  @override
  Future<List<Novel>> getNovelsByCategory(String category) async {
    return await _novelDao.getNovelsByCategory(category);
  }

  @override
  Future<List<Novel>> searchNovels(String keyword) async {
    return await _novelDao.searchNovels(keyword);
  }

  @override
  Future<List<Novel>> getRecentNovels({int limit = 10}) async {
    return await _novelDao.getRecentNovels(limit: limit);
  }

  @override
  Future<Map<String, dynamic>> getNovelStatistics() async {
    return await _novelDao.getNovelStatistics();
  }

  // 章节相关操作
  @override
  Future<void> createChapter(Chapter chapter) async {
    await _chapterDao.insertChapter(chapter);
    // 更新小说的字数和章节数统计
    await _novelDao.updateNovelWordCount(chapter.novelId);
  }

  @override
  Future<void> updateChapter(Chapter chapter) async {
    await _chapterDao.updateChapter(chapter);
    // 更新小说的字数统计
    await _novelDao.updateNovelWordCount(chapter.novelId);
  }

  @override
  Future<void> deleteChapter(String id) async {
    final chapter = await _chapterDao.getChapterById(id);
    if (chapter != null) {
      await _chapterDao.deleteChapter(id);
      // 更新小说的字数和章节数统计
      await _novelDao.updateNovelWordCount(chapter.novelId);
    }
  }

  @override
  Future<Chapter?> getChapterById(String id) async {
    return await _chapterDao.getChapterById(id);
  }

  @override
  Future<List<Chapter>> getChaptersByNovelId(String novelId) async {
    return await _chapterDao.getChaptersByNovelId(novelId);
  }

  @override
  Future<List<Chapter>> getChaptersByNovelIdAndStatus(
    String novelId,
    ChapterStatus status,
  ) async {
    return await _chapterDao.getChaptersByNovelIdAndStatus(novelId, status);
  }

  @override
  Future<void> reorderChapters(String novelId, List<String> chapterIds) async {
    await _chapterDao.reorderChapters(novelId, chapterIds);
  }

  @override
  Future<List<Chapter>> searchChapters(String novelId, String keyword) async {
    return await _chapterDao.searchChapters(novelId, keyword);
  }

  @override
  Future<Map<String, dynamic>> getChapterStatistics(String novelId) async {
    return await _chapterDao.getChapterStatistics(novelId);
  }

  @override
  Future<List<Chapter>> getRecentChapters(String novelId, {int limit = 10}) async {
    return await _chapterDao.getRecentChapters(novelId, limit: limit);
  }

  @override
  Future<Chapter?> getPreviousChapter(String novelId, int currentOrder) async {
    return await _chapterDao.getPreviousChapter(novelId, currentOrder);
  }

  @override
  Future<Chapter?> getNextChapter(String novelId, int currentOrder) async {
    return await _chapterDao.getNextChapter(novelId, currentOrder);
  }

  @override
  Future<void> batchUpdateChapterStatus(
    List<String> chapterIds,
    ChapterStatus status,
  ) async {
    await _chapterDao.batchUpdateChapterStatus(chapterIds, status);
  }
}
