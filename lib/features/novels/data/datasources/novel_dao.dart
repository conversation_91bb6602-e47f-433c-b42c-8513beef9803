import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_helper.dart';
import '../../domain/entities/novel.dart';

/// 小说数据访问对象
class NovelDao {
  final DatabaseHelper _databaseHelper;

  NovelDao(this._databaseHelper);

  /// 获取数据库实例
  Future<Database> get _database => _databaseHelper.database;

  /// 插入小说
  Future<void> insertNovel(Novel novel) async {
    final db = await _database;
    await db.insert(
      'novels',
      _novelToMap(novel),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// 更新小说
  Future<void> updateNovel(Novel novel) async {
    final db = await _database;
    await db.update(
      'novels',
      _novelToMap(novel),
      where: 'id = ?',
      whereArgs: [novel.id],
    );
  }

  /// 删除小说
  Future<void> deleteNovel(String id) async {
    final db = await _database;
    await db.delete('novels', where: 'id = ?', whereArgs: [id]);
  }

  /// 根据ID获取小说
  Future<Novel?> getNovelById(String id) async {
    final db = await _database;
    final maps = await db.query('novels', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return _mapToNovel(maps.first);
    }
    return null;
  }

  /// 获取所有小说
  Future<List<Novel>> getAllNovels() async {
    final db = await _database;
    final maps = await db.query('novels', orderBy: 'updated_at DESC');

    return maps.map((map) => _mapToNovel(map)).toList();
  }

  /// 根据状态获取小说
  Future<List<Novel>> getNovelsByStatus(NovelStatus status) async {
    final db = await _database;
    final maps = await db.query(
      'novels',
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToNovel(map)).toList();
  }

  /// 根据分类获取小说
  Future<List<Novel>> getNovelsByCategory(String category) async {
    final db = await _database;
    final maps = await db.query(
      'novels',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToNovel(map)).toList();
  }

  /// 搜索小说
  Future<List<Novel>> searchNovels(String keyword) async {
    final db = await _database;
    final maps = await db.query(
      'novels',
      where: 'title LIKE ? OR description LIKE ? OR author LIKE ?',
      whereArgs: ['%$keyword%', '%$keyword%', '%$keyword%'],
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => _mapToNovel(map)).toList();
  }

  /// 获取最近修改的小说
  Future<List<Novel>> getRecentNovels({int limit = 10}) async {
    final db = await _database;
    final maps = await db.query(
      'novels',
      orderBy: 'updated_at DESC',
      limit: limit,
    );

    return maps.map((map) => _mapToNovel(map)).toList();
  }

  /// 获取小说统计信息
  Future<Map<String, dynamic>> getNovelStatistics() async {
    final db = await _database;

    // 总小说数
    final totalCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM novels'),
        ) ??
        0;

    // 各状态小说数
    final statusCounts = <String, int>{};
    for (final status in NovelStatus.values) {
      final count =
          Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM novels WHERE status = ?', [
              status.name,
            ]),
          ) ??
          0;
      statusCounts[status.name] = count;
    }

    // 总字数
    final totalWords =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT SUM(word_count) FROM novels'),
        ) ??
        0;

    // 总章节数
    final totalChapters =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT SUM(chapter_count) FROM novels'),
        ) ??
        0;

    return {
      'totalCount': totalCount,
      'statusCounts': statusCounts,
      'totalWords': totalWords,
      'totalChapters': totalChapters,
    };
  }

  /// 更新小说字数统计
  Future<void> updateNovelWordCount(String novelId) async {
    final db = await _database;
    await db.rawUpdate(
      '''
      UPDATE novels 
      SET word_count = (
        SELECT COALESCE(SUM(word_count), 0) 
        FROM chapters 
        WHERE novel_id = ?
      ),
      chapter_count = (
        SELECT COUNT(*) 
        FROM chapters 
        WHERE novel_id = ?
      ),
      updated_at = ?
      WHERE id = ?
    ''',
      [novelId, novelId, DateTime.now().millisecondsSinceEpoch, novelId],
    );
  }

  /// 将Novel对象转换为Map
  Map<String, dynamic> _novelToMap(Novel novel) {
    return {
      'id': novel.id,
      'title': novel.title,
      'description': novel.description,
      'author': novel.author,
      'cover_image_path': novel.coverImagePath,
      'created_at': novel.createdAt.millisecondsSinceEpoch,
      'updated_at': novel.updatedAt.millisecondsSinceEpoch,
      'status': novel.status.name,
      'category': novel.category,
      'tags': jsonEncode(novel.tags),
      'word_count': novel.wordCount,
      'chapter_count': novel.chapterCount,
      'is_completed': novel.isCompleted ? 1 : 0,
      'last_read_chapter_id': novel.lastReadChapterId,
      'daily_word_goal': novel.dailyWordGoal,
      'expected_completion_date':
          novel.expectedCompletionDate?.millisecondsSinceEpoch,
      'notes': novel.notes,
    };
  }

  /// 将Map转换为Novel对象
  Novel _mapToNovel(Map<String, dynamic> map) {
    return Novel(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      author: map['author'] as String,
      coverImagePath: map['cover_image_path'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      status: NovelStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => NovelStatus.planning,
      ),
      category: map['category'] as String,
      tags: List<String>.from(jsonDecode(map['tags'] as String)),
      wordCount: map['word_count'] as int,
      chapterCount: map['chapter_count'] as int,
      isCompleted: (map['is_completed'] as int) == 1,
      lastReadChapterId: map['last_read_chapter_id'] as String?,
      dailyWordGoal: map['daily_word_goal'] as int?,
      expectedCompletionDate: map['expected_completion_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(
              map['expected_completion_date'] as int,
            )
          : null,
      notes: map['notes'] as String?,
    );
  }
}
