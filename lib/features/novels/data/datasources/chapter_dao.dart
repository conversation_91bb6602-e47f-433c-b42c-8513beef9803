import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_helper.dart';
import '../../domain/entities/chapter.dart';

/// 章节数据访问对象
class ChapterDao {
  final DatabaseHelper _databaseHelper;

  ChapterDao(this._databaseHelper);

  /// 获取数据库实例
  Future<Database> get _database => _databaseHelper.database;

  /// 插入章节
  Future<void> insertChapter(Chapter chapter) async {
    final db = await _database;
    await db.insert(
      'chapters',
      _chapterToMap(chapter),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// 更新章节
  Future<void> updateChapter(Chapter chapter) async {
    final db = await _database;
    await db.update(
      'chapters',
      _chapterToMap(chapter),
      where: 'id = ?',
      whereArgs: [chapter.id],
    );
  }

  /// 删除章节
  Future<void> deleteChapter(String id) async {
    final db = await _database;
    await db.delete('chapters', where: 'id = ?', whereArgs: [id]);
  }

  /// 根据ID获取章节
  Future<Chapter?> getChapterById(String id) async {
    final db = await _database;
    final maps = await db.query('chapters', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return _mapToChapter(maps.first);
    }
    return null;
  }

  /// 根据小说ID获取所有章节
  Future<List<Chapter>> getChaptersByNovelId(String novelId) async {
    final db = await _database;
    final maps = await db.query(
      'chapters',
      where: 'novel_id = ?',
      whereArgs: [novelId],
      orderBy: 'order_index ASC',
    );

    return maps.map((map) => _mapToChapter(map)).toList();
  }

  /// 根据小说ID和状态获取章节
  Future<List<Chapter>> getChaptersByNovelIdAndStatus(
    String novelId,
    ChapterStatus status,
  ) async {
    final db = await _database;
    final maps = await db.query(
      'chapters',
      where: 'novel_id = ? AND status = ?',
      whereArgs: [novelId, status.name],
      orderBy: 'order_index ASC',
    );

    return maps.map((map) => _mapToChapter(map)).toList();
  }

  /// 获取小说的下一个章节序号
  Future<int> getNextChapterOrder(String novelId) async {
    final db = await _database;
    final result = await db.rawQuery(
      'SELECT MAX(order_index) as max_order FROM chapters WHERE novel_id = ?',
      [novelId],
    );

    final maxOrder = result.first['max_order'] as int?;
    return (maxOrder ?? 0) + 1;
  }

  /// 重新排序章节
  Future<void> reorderChapters(String novelId, List<String> chapterIds) async {
    final db = await _database;
    await db.transaction((txn) async {
      for (int i = 0; i < chapterIds.length; i++) {
        await txn.update(
          'chapters',
          {
            'order_index': i + 1,
            'updated_at': DateTime.now().millisecondsSinceEpoch,
          },
          where: 'id = ? AND novel_id = ?',
          whereArgs: [chapterIds[i], novelId],
        );
      }
    });
  }

  /// 搜索章节
  Future<List<Chapter>> searchChapters(String novelId, String keyword) async {
    final db = await _database;
    final maps = await db.query(
      'chapters',
      where: 'novel_id = ? AND (title LIKE ? OR content LIKE ?)',
      whereArgs: [novelId, '%$keyword%', '%$keyword%'],
      orderBy: 'order_index ASC',
    );

    return maps.map((map) => _mapToChapter(map)).toList();
  }

  /// 获取章节统计信息
  Future<Map<String, dynamic>> getChapterStatistics(String novelId) async {
    final db = await _database;

    // 总章节数
    final totalCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM chapters WHERE novel_id = ?',
            [novelId],
          ),
        ) ??
        0;

    // 各状态章节数
    final statusCounts = <String, int>{};
    for (final status in ChapterStatus.values) {
      final count =
          Sqflite.firstIntValue(
            await db.rawQuery(
              'SELECT COUNT(*) FROM chapters WHERE novel_id = ? AND status = ?',
              [novelId, status.name],
            ),
          ) ??
          0;
      statusCounts[status.name] = count;
    }

    // 总字数
    final totalWords =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT SUM(word_count) FROM chapters WHERE novel_id = ?',
            [novelId],
          ),
        ) ??
        0;

    // 平均字数
    final averageWords = totalCount > 0 ? totalWords / totalCount : 0.0;

    // 总写作时长
    final totalDuration =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT SUM(writing_duration) FROM chapters WHERE novel_id = ?',
            [novelId],
          ),
        ) ??
        0;

    return {
      'totalCount': totalCount,
      'statusCounts': statusCounts,
      'totalWords': totalWords,
      'averageWords': averageWords,
      'totalDuration': totalDuration,
    };
  }

  /// 获取最近修改的章节
  Future<List<Chapter>> getRecentChapters(
    String novelId, {
    int limit = 10,
  }) async {
    final db = await _database;
    final maps = await db.query(
      'chapters',
      where: 'novel_id = ?',
      whereArgs: [novelId],
      orderBy: 'updated_at DESC',
      limit: limit,
    );

    return maps.map((map) => _mapToChapter(map)).toList();
  }

  /// 获取上一章节
  Future<Chapter?> getPreviousChapter(String novelId, int currentOrder) async {
    final db = await _database;
    final maps = await db.query(
      'chapters',
      where: 'novel_id = ? AND order_index < ?',
      whereArgs: [novelId, currentOrder],
      orderBy: 'order_index DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _mapToChapter(maps.first);
    }
    return null;
  }

  /// 获取下一章节
  Future<Chapter?> getNextChapter(String novelId, int currentOrder) async {
    final db = await _database;
    final maps = await db.query(
      'chapters',
      where: 'novel_id = ? AND order_index > ?',
      whereArgs: [novelId, currentOrder],
      orderBy: 'order_index ASC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _mapToChapter(maps.first);
    }
    return null;
  }

  /// 批量更新章节状态
  Future<void> batchUpdateChapterStatus(
    List<String> chapterIds,
    ChapterStatus status,
  ) async {
    final db = await _database;
    await db.transaction((txn) async {
      for (final id in chapterIds) {
        await txn.update(
          'chapters',
          {
            'status': status.name,
            'updated_at': DateTime.now().millisecondsSinceEpoch,
          },
          where: 'id = ?',
          whereArgs: [id],
        );
      }
    });
  }

  /// 将Chapter对象转换为Map
  Map<String, dynamic> _chapterToMap(Chapter chapter) {
    return {
      'id': chapter.id,
      'novel_id': chapter.novelId,
      'title': chapter.title,
      'content': chapter.content,
      'order_index': chapter.order,
      'created_at': chapter.createdAt.millisecondsSinceEpoch,
      'updated_at': chapter.updatedAt.millisecondsSinceEpoch,
      'status': chapter.status.name,
      'word_count': chapter.wordCount,
      'is_published': chapter.isPublished ? 1 : 0,
      'summary': chapter.summary,
      'notes': chapter.notes,
      'writing_duration': chapter.writingDuration,
      'last_read_position': chapter.lastReadPosition,
    };
  }

  /// 将Map转换为Chapter对象
  Chapter _mapToChapter(Map<String, dynamic> map) {
    return Chapter(
      id: map['id'] as String,
      novelId: map['novel_id'] as String,
      title: map['title'] as String,
      content: map['content'] as String,
      order: map['order_index'] as int,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      status: ChapterStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => ChapterStatus.draft,
      ),
      wordCount: map['word_count'] as int,
      isPublished: (map['is_published'] as int) == 1,
      summary: map['summary'] as String?,
      notes: map['notes'] as String?,
      writingDuration: map['writing_duration'] as int,
      lastReadPosition: map['last_read_position'] as int?,
    );
  }
}
