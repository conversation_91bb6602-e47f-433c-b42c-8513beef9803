import '../entities/novel.dart';
import '../entities/chapter.dart';

/// 小说仓库接口
abstract class NovelRepository {
  // 小说相关操作
  Future<void> createNovel(Novel novel);
  Future<void> updateNovel(Novel novel);
  Future<void> deleteNovel(String id);
  Future<Novel?> getNovelById(String id);
  Future<List<Novel>> getAllNovels();
  Future<List<Novel>> getNovelsByStatus(NovelStatus status);
  Future<List<Novel>> getNovelsByCategory(String category);
  Future<List<Novel>> searchNovels(String keyword);
  Future<List<Novel>> getRecentNovels({int limit = 10});
  Future<Map<String, dynamic>> getNovelStatistics();
  
  // 章节相关操作
  Future<void> createChapter(Chapter chapter);
  Future<void> updateChapter(Chapter chapter);
  Future<void> deleteChapter(String id);
  Future<Chapter?> getChapterById(String id);
  Future<List<Chapter>> getChaptersByNovelId(String novelId);
  Future<List<Chapter>> getChaptersByNovelIdAndStatus(String novelId, ChapterStatus status);
  Future<void> reorderChapters(String novelId, List<String> chapterIds);
  Future<List<Chapter>> searchChapters(String novelId, String keyword);
  Future<Map<String, dynamic>> getChapterStatistics(String novelId);
  Future<List<Chapter>> getRecentChapters(String novelId, {int limit = 10});
  Future<Chapter?> getPreviousChapter(String novelId, int currentOrder);
  Future<Chapter?> getNextChapter(String novelId, int currentOrder);
  Future<void> batchUpdateChapterStatus(List<String> chapterIds, ChapterStatus status);
}
