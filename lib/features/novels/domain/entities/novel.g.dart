// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'novel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Novel _$NovelFromJson(Map<String, dynamic> json) => Novel(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  author: json['author'] as String,
  coverImagePath: json['coverImagePath'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  status: $enumDecode(_$NovelStatusEnumMap, json['status']),
  category: json['category'] as String,
  tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
  wordCount: (json['wordCount'] as num).toInt(),
  chapterCount: (json['chapterCount'] as num).toInt(),
  isCompleted: json['isCompleted'] as bool,
  lastReadChapterId: json['lastReadChapterId'] as String?,
  dailyWordGoal: (json['dailyWordGoal'] as num?)?.toInt(),
  targetWordCount: (json['targetWordCount'] as num?)?.toInt() ?? 0,
  expectedCompletionDate: json['expectedCompletionDate'] == null
      ? null
      : DateTime.parse(json['expectedCompletionDate'] as String),
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$NovelToJson(Novel instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'author': instance.author,
  'coverImagePath': instance.coverImagePath,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'status': _$NovelStatusEnumMap[instance.status]!,
  'category': instance.category,
  'tags': instance.tags,
  'wordCount': instance.wordCount,
  'chapterCount': instance.chapterCount,
  'isCompleted': instance.isCompleted,
  'lastReadChapterId': instance.lastReadChapterId,
  'dailyWordGoal': instance.dailyWordGoal,
  'targetWordCount': instance.targetWordCount,
  'expectedCompletionDate': instance.expectedCompletionDate?.toIso8601String(),
  'notes': instance.notes,
};

const _$NovelStatusEnumMap = {
  NovelStatus.draft: 'draft',
  NovelStatus.planning: 'planning',
  NovelStatus.writing: 'writing',
  NovelStatus.paused: 'paused',
  NovelStatus.completed: 'completed',
  NovelStatus.published: 'published',
  NovelStatus.abandoned: 'abandoned',
};
