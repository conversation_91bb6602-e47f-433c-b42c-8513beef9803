import 'package:json_annotation/json_annotation.dart';

part 'chapter.g.dart';

/// 章节实体类
@JsonSerializable()
class Chapter {
  /// 唯一标识符
  final String id;
  
  /// 所属小说ID
  final String novelId;
  
  /// 章节标题
  final String title;
  
  /// 章节内容
  final String content;
  
  /// 章节序号
  final int order;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后修改时间
  final DateTime updatedAt;
  
  /// 章节状态
  final ChapterStatus status;
  
  /// 字数
  final int wordCount;
  
  /// 是否已发布
  final bool isPublished;
  
  /// 章节摘要/简介
  final String? summary;
  
  /// 章节备注
  final String? notes;
  
  /// 写作时长（分钟）
  final int writingDuration;
  
  /// 最后阅读位置
  final int? lastReadPosition;

  const Chapter({
    required this.id,
    required this.novelId,
    required this.title,
    required this.content,
    required this.order,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    required this.wordCount,
    required this.isPublished,
    this.summary,
    this.notes,
    required this.writingDuration,
    this.lastReadPosition,
  });

  /// 从JSON创建Chapter实例
  factory Chapter.fromJson(Map<String, dynamic> json) => _$ChapterFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$ChapterToJson(this);

  /// 复制并修改部分属性
  Chapter copyWith({
    String? id,
    String? novelId,
    String? title,
    String? content,
    int? order,
    DateTime? createdAt,
    DateTime? updatedAt,
    ChapterStatus? status,
    int? wordCount,
    bool? isPublished,
    String? summary,
    String? notes,
    int? writingDuration,
    int? lastReadPosition,
  }) {
    return Chapter(
      id: id ?? this.id,
      novelId: novelId ?? this.novelId,
      title: title ?? this.title,
      content: content ?? this.content,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      wordCount: wordCount ?? this.wordCount,
      isPublished: isPublished ?? this.isPublished,
      summary: summary ?? this.summary,
      notes: notes ?? this.notes,
      writingDuration: writingDuration ?? this.writingDuration,
      lastReadPosition: lastReadPosition ?? this.lastReadPosition,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Chapter && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Chapter(id: $id, title: $title, order: $order)';
}

/// 章节状态枚举
enum ChapterStatus {
  /// 草稿
  draft,
  /// 写作中
  writing,
  /// 待审核
  review,
  /// 已完成
  completed,
  /// 已发布
  published,
}
