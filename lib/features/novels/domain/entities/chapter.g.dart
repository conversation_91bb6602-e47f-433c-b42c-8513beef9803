// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chapter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Chapter _$ChapterFromJson(Map<String, dynamic> json) => Chapter(
  id: json['id'] as String,
  novelId: json['novelId'] as String,
  title: json['title'] as String,
  content: json['content'] as String,
  order: (json['order'] as num).toInt(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  status: $enumDecode(_$ChapterStatusEnumMap, json['status']),
  wordCount: (json['wordCount'] as num).toInt(),
  isPublished: json['isPublished'] as bool,
  summary: json['summary'] as String?,
  notes: json['notes'] as String?,
  writingDuration: (json['writingDuration'] as num).toInt(),
  lastReadPosition: (json['lastReadPosition'] as num?)?.toInt(),
);

Map<String, dynamic> _$ChapterToJson(Chapter instance) => <String, dynamic>{
  'id': instance.id,
  'novelId': instance.novelId,
  'title': instance.title,
  'content': instance.content,
  'order': instance.order,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'status': _$ChapterStatusEnumMap[instance.status]!,
  'wordCount': instance.wordCount,
  'isPublished': instance.isPublished,
  'summary': instance.summary,
  'notes': instance.notes,
  'writingDuration': instance.writingDuration,
  'lastReadPosition': instance.lastReadPosition,
};

const _$ChapterStatusEnumMap = {
  ChapterStatus.draft: 'draft',
  ChapterStatus.writing: 'writing',
  ChapterStatus.review: 'review',
  ChapterStatus.completed: 'completed',
  ChapterStatus.published: 'published',
};
