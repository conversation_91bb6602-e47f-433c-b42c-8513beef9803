import 'package:json_annotation/json_annotation.dart';

part 'novel.g.dart';

/// 小说实体类
@JsonSerializable()
class Novel {
  /// 唯一标识符
  final String id;

  /// 小说标题
  final String title;

  /// 小说描述/简介
  final String description;

  /// 作者
  final String author;

  /// 封面图片路径
  final String? coverImagePath;

  /// 创建时间
  final DateTime createdAt;

  /// 最后修改时间
  final DateTime updatedAt;

  /// 小说状态
  final NovelStatus status;

  /// 小说类型/分类
  final String category;

  /// 标签列表
  final List<String> tags;

  /// 总字数
  final int wordCount;

  /// 章节数量
  final int chapterCount;

  /// 是否已完结
  final bool isCompleted;

  /// 最后阅读章节ID
  final String? lastReadChapterId;

  /// 写作目标（每日字数）
  final int? dailyWordGoal;

  /// 目标总字数
  final int targetWordCount;

  /// 预计完成时间
  final DateTime? expectedCompletionDate;

  /// 备注
  final String? notes;

  const Novel({
    required this.id,
    required this.title,
    required this.description,
    required this.author,
    this.coverImagePath,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    required this.category,
    required this.tags,
    required this.wordCount,
    required this.chapterCount,
    required this.isCompleted,
    this.lastReadChapterId,
    this.dailyWordGoal,
    this.targetWordCount = 0,
    this.expectedCompletionDate,
    this.notes,
  });

  /// 从JSON创建Novel实例
  factory Novel.fromJson(Map<String, dynamic> json) => _$NovelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$NovelToJson(this);

  /// 复制并修改部分属性
  Novel copyWith({
    String? id,
    String? title,
    String? description,
    String? author,
    String? coverImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
    NovelStatus? status,
    String? category,
    List<String>? tags,
    int? wordCount,
    int? chapterCount,
    bool? isCompleted,
    String? lastReadChapterId,
    int? dailyWordGoal,
    int? targetWordCount,
    DateTime? expectedCompletionDate,
    String? notes,
  }) {
    return Novel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      author: author ?? this.author,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      wordCount: wordCount ?? this.wordCount,
      chapterCount: chapterCount ?? this.chapterCount,
      isCompleted: isCompleted ?? this.isCompleted,
      lastReadChapterId: lastReadChapterId ?? this.lastReadChapterId,
      dailyWordGoal: dailyWordGoal ?? this.dailyWordGoal,
      targetWordCount: targetWordCount ?? this.targetWordCount,
      expectedCompletionDate:
          expectedCompletionDate ?? this.expectedCompletionDate,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Novel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Novel(id: $id, title: $title, author: $author)';
}

/// 小说状态枚举
enum NovelStatus {
  /// 草稿
  draft,

  /// 构思中
  planning,

  /// 写作中
  writing,

  /// 暂停
  paused,

  /// 已完成
  completed,

  /// 已发布
  published,

  /// 已废弃
  abandoned,
}
