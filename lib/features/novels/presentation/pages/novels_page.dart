import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/theme/app_theme.dart';
import '../providers/novel_provider.dart';
import '../widgets/novel_card.dart';
import '../widgets/novel_create_dialog.dart';
import '../widgets/novel_search_bar.dart';
import '../../domain/entities/novel.dart';

/// 小说管理页面
class NovelsPage extends StatefulWidget {
  const NovelsPage({super.key});

  @override
  State<NovelsPage> createState() => _NovelsPageState();
}

class _NovelsPageState extends State<NovelsPage> {
  String _searchQuery = '';
  String _sortBy = 'updatedAt';
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NovelProvider>().loadNovels();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的小说'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(icon: const Icon(Icons.sort), onPressed: _showSortDialog),
          IconButton(icon: const Icon(Icons.add), onPressed: _showCreateDialog),
        ],
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
      floatingActionButton: AppTheme.isMobile(context)
          ? FloatingActionButton(
              onPressed: _showCreateDialog,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildMobileLayout() {
    return Consumer<NovelProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final novels = _getFilteredNovels(provider.novels);

        if (novels.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadNovels(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: novels.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: NovelCard(
                  novel: novels[index],
                  onTap: () => _openNovel(novels[index]),
                  onEdit: () => _editNovel(novels[index]),
                  onDelete: () => _deleteNovel(novels[index]),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildTabletLayout() {
    return Consumer<NovelProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final novels = _getFilteredNovels(provider.novels);

        if (novels.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadNovels(),
          child: ResponsiveGrid(
            tabletColumns: 2,
            children: novels
                .map(
                  (novel) => NovelCard(
                    novel: novel,
                    onTap: () => _openNovel(novel),
                    onEdit: () => _editNovel(novel),
                    onDelete: () => _deleteNovel(novel),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildDesktopLayout() {
    return Consumer<NovelProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final novels = _getFilteredNovels(provider.novels);

        if (novels.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadNovels(),
          child: ResponsiveGrid(
            desktopColumns: 3,
            children: novels
                .map(
                  (novel) => NovelCard(
                    novel: novel,
                    onTap: () => _openNovel(novel),
                    onEdit: () => _editNovel(novel),
                    onDelete: () => _deleteNovel(novel),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '还没有小说作品',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击右上角的 + 按钮开始创作你的第一部小说',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showCreateDialog,
            icon: const Icon(Icons.add),
            label: const Text('创建新小说'),
          ),
        ],
      ),
    );
  }

  List<Novel> _getFilteredNovels(List<Novel> novels) {
    var filtered = novels.where((novel) {
      if (_searchQuery.isEmpty) return true;
      return novel.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          novel.description.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    // 排序
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'title':
          comparison = a.title.compareTo(b.title);
          break;
        case 'createdAt':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'updatedAt':
        default:
          comparison = a.updatedAt.compareTo(b.updatedAt);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => NovelSearchBar(
        initialQuery: _searchQuery,
        onSearch: (query) {
          setState(() {
            _searchQuery = query;
          });
        },
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('排序方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('按标题'),
              value: 'title',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按创建时间'),
              value: 'createdAt',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('按更新时间'),
              value: 'updatedAt',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            SwitchListTile(
              title: const Text('升序排列'),
              value: _sortAscending,
              onChanged: (value) {
                setState(() {
                  _sortAscending = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => const NovelCreateDialog(),
    );
  }

  void _openNovel(Novel novel) {
    // TODO: 导航到小说详情页面
    Navigator.of(context).pushNamed('/novels/${novel.id}');
  }

  void _editNovel(Novel novel) {
    // TODO: 显示编辑对话框
    showDialog(
      context: context,
      builder: (context) => NovelCreateDialog(novel: novel),
    );
  }

  void _deleteNovel(Novel novel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除小说'),
        content: Text('确定要删除小说《${novel.title}》吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              context.read<NovelProvider>().deleteNovel(novel.id);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
