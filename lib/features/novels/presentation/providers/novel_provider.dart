import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/novel.dart';
import '../../domain/entities/chapter.dart';
import '../../domain/repositories/novel_repository.dart';

/// 小说状态管理
class NovelProvider extends ChangeNotifier {
  final NovelRepository _repository;
  final Uuid _uuid = const Uuid();

  NovelProvider({required NovelRepository repository}) : _repository = repository;

  // 状态变量
  List<Novel> _novels = [];
  Novel? _currentNovel;
  List<Chapter> _chapters = [];
  Chapter? _currentChapter;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _statistics;

  // Getters
  List<Novel> get novels => _novels;
  Novel? get currentNovel => _currentNovel;
  List<Chapter> get chapters => _chapters;
  Chapter? get currentChapter => _currentChapter;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic>? get statistics => _statistics;

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // 小说相关操作
  /// 加载所有小说
  Future<void> loadNovels() async {
    try {
      _setLoading(true);
      _setError(null);
      _novels = await _repository.getAllNovels();
      notifyListeners();
    } catch (e) {
      _setError('加载小说列表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 创建新小说
  Future<bool> createNovel({
    required String title,
    required String description,
    required String author,
    required String category,
    List<String> tags = const [],
    String? coverImagePath,
    int? dailyWordGoal,
    DateTime? expectedCompletionDate,
    String? notes,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final novel = Novel(
        id: _uuid.v4(),
        title: title,
        description: description,
        author: author,
        coverImagePath: coverImagePath,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: NovelStatus.planning,
        category: category,
        tags: tags,
        wordCount: 0,
        chapterCount: 0,
        isCompleted: false,
        dailyWordGoal: dailyWordGoal,
        expectedCompletionDate: expectedCompletionDate,
        notes: notes,
      );

      await _repository.createNovel(novel);
      _novels.insert(0, novel);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('创建小说失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 更新小说
  Future<bool> updateNovel(Novel novel) async {
    try {
      _setLoading(true);
      _setError(null);

      final updatedNovel = novel.copyWith(updatedAt: DateTime.now());
      await _repository.updateNovel(updatedNovel);

      final index = _novels.indexWhere((n) => n.id == novel.id);
      if (index != -1) {
        _novels[index] = updatedNovel;
      }

      if (_currentNovel?.id == novel.id) {
        _currentNovel = updatedNovel;
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError('更新小说失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 删除小说
  Future<bool> deleteNovel(String id) async {
    try {
      _setLoading(true);
      _setError(null);

      await _repository.deleteNovel(id);
      _novels.removeWhere((novel) => novel.id == id);

      if (_currentNovel?.id == id) {
        _currentNovel = null;
        _chapters.clear();
        _currentChapter = null;
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError('删除小说失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 设置当前小说
  Future<void> setCurrentNovel(String novelId) async {
    try {
      _setLoading(true);
      _setError(null);

      _currentNovel = await _repository.getNovelById(novelId);
      if (_currentNovel != null) {
        await loadChapters(novelId);
      }
      notifyListeners();
    } catch (e) {
      _setError('加载小说详情失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 搜索小说
  Future<void> searchNovels(String keyword) async {
    try {
      _setLoading(true);
      _setError(null);

      if (keyword.isEmpty) {
        await loadNovels();
      } else {
        _novels = await _repository.searchNovels(keyword);
        notifyListeners();
      }
    } catch (e) {
      _setError('搜索小说失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  // 章节相关操作
  /// 加载章节列表
  Future<void> loadChapters(String novelId) async {
    try {
      _setError(null);
      _chapters = await _repository.getChaptersByNovelId(novelId);
      notifyListeners();
    } catch (e) {
      _setError('加载章节列表失败: $e');
    }
  }

  /// 创建新章节
  Future<bool> createChapter({
    required String novelId,
    required String title,
    String content = '',
    String? summary,
    String? notes,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      // 获取下一个章节序号
      final nextOrder = _chapters.length + 1;

      final chapter = Chapter(
        id: _uuid.v4(),
        novelId: novelId,
        title: title,
        content: content,
        order: nextOrder,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: ChapterStatus.draft,
        wordCount: _calculateWordCount(content),
        isPublished: false,
        summary: summary,
        notes: notes,
        writingDuration: 0,
      );

      await _repository.createChapter(chapter);
      _chapters.add(chapter);
      
      // 更新当前小说信息
      if (_currentNovel != null) {
        _currentNovel = _currentNovel!.copyWith(
          chapterCount: _chapters.length,
          updatedAt: DateTime.now(),
        );
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError('创建章节失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 更新章节
  Future<bool> updateChapter(Chapter chapter) async {
    try {
      _setLoading(true);
      _setError(null);

      final updatedChapter = chapter.copyWith(
        updatedAt: DateTime.now(),
        wordCount: _calculateWordCount(chapter.content),
      );

      await _repository.updateChapter(updatedChapter);

      final index = _chapters.indexWhere((c) => c.id == chapter.id);
      if (index != -1) {
        _chapters[index] = updatedChapter;
      }

      if (_currentChapter?.id == chapter.id) {
        _currentChapter = updatedChapter;
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError('更新章节失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 删除章节
  Future<bool> deleteChapter(String id) async {
    try {
      _setLoading(true);
      _setError(null);

      await _repository.deleteChapter(id);
      _chapters.removeWhere((chapter) => chapter.id == id);

      if (_currentChapter?.id == id) {
        _currentChapter = null;
      }

      // 更新当前小说信息
      if (_currentNovel != null) {
        _currentNovel = _currentNovel!.copyWith(
          chapterCount: _chapters.length,
          updatedAt: DateTime.now(),
        );
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError('删除章节失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 设置当前章节
  void setCurrentChapter(Chapter chapter) {
    _currentChapter = chapter;
    notifyListeners();
  }

  /// 重新排序章节
  Future<bool> reorderChapters(List<String> chapterIds) async {
    try {
      _setLoading(true);
      _setError(null);

      if (_currentNovel != null) {
        await _repository.reorderChapters(_currentNovel!.id, chapterIds);
        await loadChapters(_currentNovel!.id);
      }

      return true;
    } catch (e) {
      _setError('重新排序章节失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 加载统计信息
  Future<void> loadStatistics() async {
    try {
      _setError(null);
      _statistics = await _repository.getNovelStatistics();
      notifyListeners();
    } catch (e) {
      _setError('加载统计信息失败: $e');
    }
  }

  /// 计算字数
  int _calculateWordCount(String content) {
    if (content.isEmpty) return 0;
    // 简单的中文字数统计，去除空白字符
    return content.replaceAll(RegExp(r'\s'), '').length;
  }

  /// 获取下一章节
  Chapter? getNextChapter(Chapter currentChapter) {
    final currentIndex = _chapters.indexWhere((c) => c.id == currentChapter.id);
    if (currentIndex != -1 && currentIndex < _chapters.length - 1) {
      return _chapters[currentIndex + 1];
    }
    return null;
  }

  /// 获取上一章节
  Chapter? getPreviousChapter(Chapter currentChapter) {
    final currentIndex = _chapters.indexWhere((c) => c.id == currentChapter.id);
    if (currentIndex > 0) {
      return _chapters[currentIndex - 1];
    }
    return null;
  }
}
