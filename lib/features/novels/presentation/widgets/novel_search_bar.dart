import 'package:flutter/material.dart';

/// 小说搜索栏组件
class NovelSearchBar extends StatefulWidget {
  final String initialQuery;
  final Function(String) onSearch;

  const NovelSearchBar({
    super.key,
    required this.initialQuery,
    required this.onSearch,
  });

  @override
  State<NovelSearchBar> createState() => _NovelSearchBarState();
}

class _NovelSearchBarState extends State<NovelSearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('搜索小说'),
      content: TextField(
        controller: _controller,
        decoration: const InputDecoration(
          hintText: '输入小说标题或简介关键词',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        autofocus: true,
        onSubmitted: (value) {
          widget.onSearch(value.trim());
          Navigator.of(context).pop();
        },
      ),
      actions: [
        TextButton(
          onPressed: () {
            _controller.clear();
            widget.onSearch('');
            Navigator.of(context).pop();
          },
          child: const Text('清除'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onSearch(_controller.text.trim());
            Navigator.of(context).pop();
          },
          child: const Text('搜索'),
        ),
      ],
    );
  }
}
