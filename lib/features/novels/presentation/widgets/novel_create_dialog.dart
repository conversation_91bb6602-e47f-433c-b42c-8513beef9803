import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../domain/entities/novel.dart';
import '../providers/novel_provider.dart';

/// 小说创建/编辑对话框
class NovelCreateDialog extends StatefulWidget {
  final Novel? novel; // 如果为null则是创建，否则是编辑

  const NovelCreateDialog({super.key, this.novel});

  @override
  State<NovelCreateDialog> createState() => _NovelCreateDialogState();
}

class _NovelCreateDialogState extends State<NovelCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _targetWordCountController = TextEditingController();

  NovelStatus _status = NovelStatus.draft;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.novel != null) {
      _titleController.text = widget.novel!.title;
      _descriptionController.text = widget.novel!.description;
      _targetWordCountController.text = widget.novel!.targetWordCount
          .toString();
      _status = widget.novel!.status;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _targetWordCountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.novel != null;

    return AlertDialog(
      title: Text(isEditing ? '编辑小说' : '创建新小说'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: '小说标题',
                    hintText: '请输入小说标题',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入小说标题';
                    }
                    if (value.trim().length > 100) {
                      return '标题不能超过100个字符';
                    }
                    return null;
                  },
                  maxLength: 100,
                ),
                const SizedBox(height: 16),

                // 描述
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: '小说简介',
                    hintText: '请输入小说简介（可选）',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  maxLength: 500,
                  validator: (value) {
                    if (value != null && value.length > 500) {
                      return '简介不能超过500个字符';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // 目标字数
                TextFormField(
                  controller: _targetWordCountController,
                  decoration: const InputDecoration(
                    labelText: '目标字数',
                    hintText: '请输入目标字数（可选）',
                    border: OutlineInputBorder(),
                    suffixText: '字',
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final wordCount = int.tryParse(value);
                      if (wordCount == null || wordCount <= 0) {
                        return '请输入有效的字数';
                      }
                      if (wordCount > 10000000) {
                        return '目标字数不能超过1000万字';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // 状态选择
                DropdownButtonFormField<NovelStatus>(
                  value: _status,
                  decoration: const InputDecoration(
                    labelText: '小说状态',
                    border: OutlineInputBorder(),
                  ),
                  items: NovelStatus.values.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(_getStatusText(status)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _status = value;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveNovel,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? '保存' : '创建'),
        ),
      ],
    );
  }

  String _getStatusText(NovelStatus status) {
    switch (status) {
      case NovelStatus.draft:
        return '草稿';
      case NovelStatus.planning:
        return '构思中';
      case NovelStatus.writing:
        return '连载中';
      case NovelStatus.completed:
        return '已完结';
      case NovelStatus.paused:
        return '暂停';
      case NovelStatus.published:
        return '已发布';
      case NovelStatus.abandoned:
        return '已废弃';
    }
  }

  Future<void> _saveNovel() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final title = _titleController.text.trim();
      final description = _descriptionController.text.trim();
      final targetWordCount =
          int.tryParse(_targetWordCountController.text) ?? 0;

      final provider = context.read<NovelProvider>();

      if (widget.novel != null) {
        // 编辑现有小说
        final updatedNovel = widget.novel!.copyWith(
          title: title,
          description: description,
          targetWordCount: targetWordCount,
          status: _status,
          updatedAt: DateTime.now(),
        );
        await provider.updateNovel(updatedNovel);
      } else {
        // 创建新小说
        await provider.createNovel(
          title: title,
          description: description,
          author: '作者', // 默认作者，后续可以从设置中获取
          category: '未分类', // 默认分类
          tags: [], // 空标签列表
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.novel != null ? '小说已更新' : '小说创建成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('操作失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
