import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/widgets/theme_switcher.dart';

import '../../../novels/presentation/providers/novel_provider.dart';
import '../../../materials/presentation/providers/material_provider.dart';
import '../../../settings/presentation/providers/settings_provider.dart';
import '../widgets/home_dashboard.dart';
import '../widgets/quick_actions.dart';
import '../widgets/recent_activity.dart';

/// 主页
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    // 使用WidgetsBinding.instance.addPostFrameCallback来延迟数据加载
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final novelProvider = context.read<NovelProvider>();
    final materialProvider = context.read<MaterialProvider>();

    // 并行加载数据
    await Future.wait([
      novelProvider.loadNovels(),
      novelProvider.loadStatistics(),
      materialProvider.loadMaterials(),
      materialProvider.loadStatistics(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: ResponsiveContainer(
        child: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Row(
        children: [
          Icon(
            Icons.auto_stories,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Text('NovelICE'),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _showSearchDialog(context),
          tooltip: '搜索',
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _showNotifications(context),
          tooltip: '通知',
        ),
        const ThemeToggleButton(),
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => _navigateToSettings(context),
          tooltip: '设置',
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 24),
          const QuickActions(),
          const SizedBox(height: 24),
          const HomeDashboard(),
          const SizedBox(height: 24),
          const RecentActivity(),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 32),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Expanded(
                flex: 2,
                child: Column(
                  children: [
                    QuickActions(),
                    SizedBox(height: 24),
                    HomeDashboard(),
                  ],
                ),
              ),
              const SizedBox(width: 24),
              const Expanded(flex: 1, child: RecentActivity()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 40),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Expanded(
                flex: 3,
                child: Column(
                  children: [
                    QuickActions(),
                    SizedBox(height: 32),
                    HomeDashboard(),
                  ],
                ),
              ),
              const SizedBox(width: 32),
              const Expanded(flex: 2, child: RecentActivity()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        final hour = DateTime.now().hour;
        String greeting;
        if (hour < 6) {
          greeting = '夜深了';
        } else if (hour < 12) {
          greeting = '早上好';
        } else if (hour < 18) {
          greeting = '下午好';
        } else {
          greeting = '晚上好';
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$greeting，创作者',
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              '今天也要继续创作精彩的故事哦！',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: '搜索小说、章节、角色、素材...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现搜索功能
              Navigator.of(context).pop();
            },
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('通知'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.check_circle, color: Colors.green),
              title: Text('今日写作目标已完成'),
              subtitle: Text('恭喜你完成了1000字的写作目标！'),
            ),
            ListTile(
              leading: Icon(Icons.backup, color: Colors.blue),
              title: Text('自动备份完成'),
              subtitle: Text('你的作品已自动备份到本地'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _navigateToSettings(BuildContext context) {
    // TODO: 导航到设置页面
    Navigator.of(context).pushNamed('/settings');
  }
}
