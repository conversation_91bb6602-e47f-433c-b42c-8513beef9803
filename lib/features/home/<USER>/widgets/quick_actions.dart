import 'package:flutter/material.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/theme/app_theme.dart';

/// 快速操作组件
class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速操作',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          children: [
            _buildActionCard(
              context,
              title: '新建小说',
              subtitle: '开始新的创作',
              icon: Icons.add_box,
              color: AppTheme.primaryColor,
              onTap: () => _navigateToCreateNovel(context),
            ),
            _buildActionCard(
              context,
              title: '继续写作',
              subtitle: '回到上次编辑',
              icon: Icons.edit,
              color: AppTheme.successColor,
              onTap: () => _navigateToLastChapter(context),
            ),
            _buildActionCard(
              context,
              title: '添加素材',
              subtitle: '记录灵感想法',
              icon: Icons.lightbulb,
              color: AppTheme.warningColor,
              onTap: () => _navigateToAddMaterial(context),
            ),
            _buildActionCard(
              context,
              title: '角色管理',
              subtitle: '创建和编辑角色',
              icon: Icons.people,
              color: AppTheme.infoColor,
              onTap: () => _navigateToCharacters(context),
            ),
            _buildActionCard(
              context,
              title: '世界观设定',
              subtitle: '构建故事世界',
              icon: Icons.public,
              color: AppTheme.secondaryColor,
              onTap: () => _navigateToWorldSettings(context),
            ),
            _buildActionCard(
              context,
              title: '时间线',
              subtitle: '管理故事时间线',
              icon: Icons.timeline,
              color: AppTheme.neutralGray600,
              onTap: () => _navigateToTimeline(context),
            ),
            _buildActionCard(
              context,
              title: '写作统计',
              subtitle: '查看创作数据',
              icon: Icons.analytics,
              color: AppTheme.errorColor,
              onTap: () => _navigateToStatistics(context),
            ),
            _buildActionCard(
              context,
              title: '导入导出',
              subtitle: '备份和同步',
              icon: Icons.import_export,
              color: AppTheme.neutralGray500,
              onTap: () => _navigateToImportExport(context),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ResponsiveCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _navigateToCreateNovel(BuildContext context) {
    // TODO: 导航到创建小说页面
    Navigator.of(context).pushNamed('/novels/create');
  }

  void _navigateToLastChapter(BuildContext context) {
    // TODO: 导航到最后编辑的章节
    Navigator.of(context).pushNamed('/novels/continue');
  }

  void _navigateToAddMaterial(BuildContext context) {
    // TODO: 导航到添加素材页面
    Navigator.of(context).pushNamed('/materials/create');
  }

  void _navigateToCharacters(BuildContext context) {
    // TODO: 导航到角色管理页面
    Navigator.of(context).pushNamed('/wiki/characters');
  }

  void _navigateToWorldSettings(BuildContext context) {
    // TODO: 导航到世界观设定页面
    Navigator.of(context).pushNamed('/wiki/world-settings');
  }

  void _navigateToTimeline(BuildContext context) {
    // TODO: 导航到时间线页面
    Navigator.of(context).pushNamed('/wiki/timeline');
  }

  void _navigateToStatistics(BuildContext context) {
    // TODO: 导航到统计页面
    Navigator.of(context).pushNamed('/statistics');
  }

  void _navigateToImportExport(BuildContext context) {
    // TODO: 导航到导入导出页面
    Navigator.of(context).pushNamed('/settings/import-export');
  }
}
