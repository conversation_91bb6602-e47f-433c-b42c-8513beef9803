import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../novels/presentation/providers/novel_provider.dart';
import '../../../materials/presentation/providers/material_provider.dart';

/// 主页仪表板
class HomeDashboard extends StatelessWidget {
  const HomeDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '创作概览',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          children: [
            _buildStatCard(
              context,
              title: '小说总数',
              icon: Icons.book,
              color: AppTheme.primaryColor,
              builder: (context) {
                return Consumer<NovelProvider>(
                  builder: (context, provider, child) {
                    final stats = provider.statistics;
                    final count = stats?['totalCount'] ?? 0;
                    return Text(
                      count.toString(),
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                    );
                  },
                );
              },
            ),
            _buildStatCard(
              context,
              title: '总字数',
              icon: Icons.text_fields,
              color: AppTheme.successColor,
              builder: (context) {
                return Consumer<NovelProvider>(
                  builder: (context, provider, child) {
                    final stats = provider.statistics;
                    final wordCount = stats?['totalWords'] ?? 0;
                    return Text(
                      AppUtils.formatWordCount(wordCount),
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.successColor,
                          ),
                    );
                  },
                );
              },
            ),
            _buildStatCard(
              context,
              title: '章节总数',
              icon: Icons.article,
              color: AppTheme.infoColor,
              builder: (context) {
                return Consumer<NovelProvider>(
                  builder: (context, provider, child) {
                    final stats = provider.statistics;
                    final chapterCount = stats?['totalChapters'] ?? 0;
                    return Text(
                      chapterCount.toString(),
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.infoColor,
                          ),
                    );
                  },
                );
              },
            ),
            _buildStatCard(
              context,
              title: '素材数量',
              icon: Icons.inventory_2,
              color: AppTheme.warningColor,
              builder: (context) {
                return Consumer<MaterialProvider>(
                  builder: (context, provider, child) {
                    final stats = provider.statistics;
                    final materialCount = stats?['totalCount'] ?? 0;
                    return Text(
                      materialCount.toString(),
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.warningColor,
                          ),
                    );
                  },
                );
              },
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildProgressSection(context),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required Widget Function(BuildContext) builder,
  }) {
    return ResponsiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Icon(Icons.trending_up, color: color.withOpacity(0.5), size: 16),
            ],
          ),
          const SizedBox(height: 12),
          builder(context),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '写作进度',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Consumer<NovelProvider>(
          builder: (context, provider, child) {
            final novels = provider.novels.take(3).toList();

            if (novels.isEmpty) {
              return _buildEmptyState(context);
            }

            return Column(
              children: novels
                  .map((novel) => _buildProgressItem(context, novel))
                  .toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildProgressItem(BuildContext context, novel) {
    final progress = novel.wordCount / (novel.dailyWordGoal ?? 1000);
    final progressPercentage = (progress * 100).clamp(0, 100);

    return ResponsiveCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      novel.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${AppUtils.formatWordCount(novel.wordCount)} / ${novel.chapterCount}章',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(novel.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getStatusText(novel.status),
                  style: TextStyle(
                    color: _getStatusColor(novel.status),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: progress.clamp(0.0, 1.0),
                  backgroundColor: Theme.of(
                    context,
                  ).colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getStatusColor(novel.status),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${progressPercentage.toInt()}%',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return ResponsiveCard(
      child: Column(
        children: [
          Icon(
            Icons.auto_stories_outlined,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            '还没有小说',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '创建你的第一部小说开始创作之旅吧！',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              // TODO: 导航到创建小说页面
            },
            icon: const Icon(Icons.add),
            label: const Text('创建小说'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(status) {
    switch (status.toString()) {
      case 'NovelStatus.planning':
        return AppTheme.warningColor;
      case 'NovelStatus.writing':
        return AppTheme.infoColor;
      case 'NovelStatus.paused':
        return AppTheme.neutralGray500;
      case 'NovelStatus.completed':
        return AppTheme.successColor;
      case 'NovelStatus.published':
        return AppTheme.primaryColor;
      case 'NovelStatus.abandoned':
        return AppTheme.errorColor;
      default:
        return AppTheme.neutralGray500;
    }
  }

  String _getStatusText(status) {
    switch (status.toString()) {
      case 'NovelStatus.planning':
        return '构思中';
      case 'NovelStatus.writing':
        return '写作中';
      case 'NovelStatus.paused':
        return '暂停';
      case 'NovelStatus.completed':
        return '已完成';
      case 'NovelStatus.published':
        return '已发布';
      case 'NovelStatus.abandoned':
        return '已废弃';
      default:
        return '未知';
    }
  }
}
