import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../novels/presentation/providers/novel_provider.dart';
import '../../../materials/presentation/providers/material_provider.dart';

/// 最近活动组件
class RecentActivity extends StatelessWidget {
  const RecentActivity({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '最近活动',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () => _navigateToAllActivity(context),
              child: const Text('查看全部'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildActivityList(context),
      ],
    );
  }

  Widget _buildActivityList(BuildContext context) {
    return Consumer2<NovelProvider, MaterialProvider>(
      builder: (context, novelProvider, materialProvider, child) {
        final activities = <ActivityItem>[];

        // 添加最近的小说活动
        for (final novel in novelProvider.novels.take(3)) {
          activities.add(ActivityItem(
            type: ActivityType.novel,
            title: '编辑了小说《${novel.title}》',
            subtitle: '${AppUtils.formatWordCount(novel.wordCount)} 字',
            time: novel.updatedAt,
            icon: Icons.book,
            color: AppTheme.primaryColor,
            onTap: () => _navigateToNovel(context, novel.id),
          ));
        }

        // 添加最近的素材活动
        for (final material in materialProvider.getRecentMaterials(limit: 3)) {
          activities.add(ActivityItem(
            type: ActivityType.material,
            title: '添加了素材《${material.title}》',
            subtitle: _getMaterialTypeText(material.type),
            time: material.createdAt,
            icon: Icons.lightbulb,
            color: AppTheme.warningColor,
            onTap: () => _navigateToMaterial(context, material.id),
          ));
        }

        // 按时间排序
        activities.sort((a, b) => b.time.compareTo(a.time));

        if (activities.isEmpty) {
          return _buildEmptyState(context);
        }

        return Column(
          children: activities.take(6).map((activity) => _buildActivityItem(context, activity)).toList(),
        );
      },
    );
  }

  Widget _buildActivityItem(BuildContext context, ActivityItem activity) {
    return ResponsiveCard(
      padding: const EdgeInsets.all(12),
      onTap: activity.onTap,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: activity.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              activity.icon,
              color: activity.color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  activity.subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Text(
            AppUtils.formatRelativeTime(activity.time),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return ResponsiveCard(
      child: Column(
        children: [
          Icon(
            Icons.history,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无活动记录',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始创作后这里会显示你的活动记录',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getMaterialTypeText(type) {
    switch (type.toString()) {
      case 'MaterialType.inspiration':
        return '灵感';
      case 'MaterialType.idea':
        return '想法';
      case 'MaterialType.note':
        return '笔记';
      case 'MaterialType.research':
        return '资料';
      case 'MaterialType.reference':
        return '参考';
      case 'MaterialType.outline':
        return '大纲';
      case 'MaterialType.draft':
        return '草稿';
      case 'MaterialType.other':
        return '其他';
      default:
        return '素材';
    }
  }

  void _navigateToAllActivity(BuildContext context) {
    // TODO: 导航到全部活动页面
    Navigator.of(context).pushNamed('/activity');
  }

  void _navigateToNovel(BuildContext context, String novelId) {
    // TODO: 导航到小说详情页面
    Navigator.of(context).pushNamed('/novels/$novelId');
  }

  void _navigateToMaterial(BuildContext context, String materialId) {
    // TODO: 导航到素材详情页面
    Navigator.of(context).pushNamed('/materials/$materialId');
  }
}

/// 活动类型
enum ActivityType {
  novel,
  chapter,
  character,
  worldSetting,
  timeline,
  material,
}

/// 活动项
class ActivityItem {
  final ActivityType type;
  final String title;
  final String subtitle;
  final DateTime time;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const ActivityItem({
    required this.type,
    required this.title,
    required this.subtitle,
    required this.time,
    required this.icon,
    required this.color,
    this.onTap,
  });
}
