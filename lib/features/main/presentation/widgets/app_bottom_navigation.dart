import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../pages/main_layout.dart';

/// 应用底部导航栏
class AppBottomNavigation extends StatelessWidget {
  final String currentLocation;

  const AppBottomNavigation({
    super.key,
    required this.currentLocation,
  });

  @override
  Widget build(BuildContext context) {
    final currentIndex = NavigationConfig.getCurrentIndex(currentLocation);

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: (index) {
        final route = NavigationConfig.getRouteByIndex(index);
        if (currentLocation != route) {
          context.go(route);
        }
      },
      items: NavigationConfig.mainItems
          .map((item) => BottomNavigationBarItem(
                icon: Icon(item.icon),
                label: item.label,
              ))
          .toList(),
    );
  }
}
