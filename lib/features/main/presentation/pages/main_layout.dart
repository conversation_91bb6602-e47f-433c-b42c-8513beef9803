import 'package:flutter/material.dart';
import '../../../../core/widgets/responsive_layout.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/app_sidebar.dart';
import '../widgets/app_bottom_navigation.dart';

/// 主应用布局
class MainLayout extends StatefulWidget {
  final Widget child;
  final String location;

  const MainLayout({super.key, required this.child, required this.location});

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
      drawer: AppTheme.isMobile(context) ? const AppSidebar() : null,
      bottomNavigationBar: AppTheme.isMobile(context)
          ? AppBottomNavigation(currentLocation: widget.location)
          : null,
    );
  }

  Widget _buildMobileLayout() {
    return widget.child;
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        const SizedBox(width: 280, child: AppSidebar()),
        Expanded(child: widget.child),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        const SizedBox(width: 280, child: AppSidebar()),
        Expanded(child: widget.child),
      ],
    );
  }
}

/// 导航项配置
class NavigationConfig {
  static const List<NavigationItem> mainItems = [
    NavigationItem(icon: Icons.home, label: '首页', route: '/'),
    NavigationItem(icon: Icons.book, label: '小说', route: '/novels'),
    NavigationItem(icon: Icons.account_tree, label: 'Wiki', route: '/wiki'),
    NavigationItem(icon: Icons.inventory_2, label: '素材库', route: '/materials'),
    NavigationItem(icon: Icons.analytics, label: '统计', route: '/statistics'),
  ];

  static const List<NavigationItem> secondaryItems = [
    NavigationItem(icon: Icons.settings, label: '设置', route: '/settings'),
    NavigationItem(icon: Icons.help, label: '帮助', route: '/help'),
  ];

  /// 根据路由获取当前索引
  static int getCurrentIndex(String location) {
    for (int i = 0; i < mainItems.length; i++) {
      final route = mainItems[i].route;
      if (route != null && location.startsWith(route)) {
        return i;
      }
    }
    return 0;
  }

  /// 根据索引获取路由
  static String getRouteByIndex(int index) {
    if (index >= 0 && index < mainItems.length) {
      return mainItems[index].route ?? '/';
    }
    return '/';
  }
}
