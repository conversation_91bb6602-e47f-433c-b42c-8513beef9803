import 'package:json_annotation/json_annotation.dart';

part 'writing_statistics.g.dart';

/// 写作统计实体类
@JsonSerializable()
class WritingStatistics {
  /// 统计日期
  final DateTime date;
  
  /// 所属小说ID（可选，全局统计为null）
  final String? novelId;
  
  /// 当日写作字数
  final int wordsWritten;
  
  /// 当日写作时长（分钟）
  final int writingDuration;
  
  /// 当日创建章节数
  final int chaptersCreated;
  
  /// 当日修改章节数
  final int chaptersModified;
  
  /// 当日删除字数
  final int wordsDeleted;
  
  /// 当日净增字数
  final int netWordsAdded;
  
  /// 写作会话列表
  final List<WritingSession> sessions;
  
  /// 是否达成每日目标
  final bool goalAchieved;
  
  /// 每日目标字数
  final int dailyGoal;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后修改时间
  final DateTime updatedAt;

  const WritingStatistics({
    required this.date,
    this.novelId,
    required this.wordsWritten,
    required this.writingDuration,
    required this.chaptersCreated,
    required this.chaptersModified,
    required this.wordsDeleted,
    required this.netWordsAdded,
    required this.sessions,
    required this.goalAchieved,
    required this.dailyGoal,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 从JSON创建WritingStatistics实例
  factory WritingStatistics.fromJson(Map<String, dynamic> json) => 
      _$WritingStatisticsFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$WritingStatisticsToJson(this);

  /// 创建空的统计记录
  factory WritingStatistics.empty({
    required DateTime date,
    String? novelId,
    required int dailyGoal,
  }) {
    return WritingStatistics(
      date: date,
      novelId: novelId,
      wordsWritten: 0,
      writingDuration: 0,
      chaptersCreated: 0,
      chaptersModified: 0,
      wordsDeleted: 0,
      netWordsAdded: 0,
      sessions: [],
      goalAchieved: false,
      dailyGoal: dailyGoal,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// 复制并修改部分属性
  WritingStatistics copyWith({
    DateTime? date,
    String? novelId,
    int? wordsWritten,
    int? writingDuration,
    int? chaptersCreated,
    int? chaptersModified,
    int? wordsDeleted,
    int? netWordsAdded,
    List<WritingSession>? sessions,
    bool? goalAchieved,
    int? dailyGoal,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WritingStatistics(
      date: date ?? this.date,
      novelId: novelId ?? this.novelId,
      wordsWritten: wordsWritten ?? this.wordsWritten,
      writingDuration: writingDuration ?? this.writingDuration,
      chaptersCreated: chaptersCreated ?? this.chaptersCreated,
      chaptersModified: chaptersModified ?? this.chaptersModified,
      wordsDeleted: wordsDeleted ?? this.wordsDeleted,
      netWordsAdded: netWordsAdded ?? this.netWordsAdded,
      sessions: sessions ?? this.sessions,
      goalAchieved: goalAchieved ?? this.goalAchieved,
      dailyGoal: dailyGoal ?? this.dailyGoal,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 计算平均写作速度（字/分钟）
  double get averageWritingSpeed {
    if (writingDuration == 0) return 0.0;
    return wordsWritten / writingDuration;
  }

  /// 计算目标完成百分比
  double get goalCompletionPercentage {
    if (dailyGoal == 0) return 0.0;
    return (wordsWritten / dailyGoal * 100).clamp(0.0, 100.0);
  }

  @override
  String toString() => 'WritingStatistics(date: $date, words: $wordsWritten, duration: $writingDuration)';
}

/// 写作会话
@JsonSerializable()
class WritingSession {
  /// 会话ID
  final String id;
  
  /// 开始时间
  final DateTime startTime;
  
  /// 结束时间
  final DateTime? endTime;
  
  /// 会话时长（分钟）
  final int duration;
  
  /// 会话中写作字数
  final int wordsWritten;
  
  /// 会话中删除字数
  final int wordsDeleted;
  
  /// 相关章节ID
  final String? chapterId;
  
  /// 会话类型
  final SessionType type;
  
  /// 会话备注
  final String? notes;

  const WritingSession({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.duration,
    required this.wordsWritten,
    required this.wordsDeleted,
    this.chapterId,
    required this.type,
    this.notes,
  });

  /// 从JSON创建WritingSession实例
  factory WritingSession.fromJson(Map<String, dynamic> json) => 
      _$WritingSessionFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$WritingSessionToJson(this);

  /// 复制并修改部分属性
  WritingSession copyWith({
    String? id,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
    int? wordsWritten,
    int? wordsDeleted,
    String? chapterId,
    SessionType? type,
    String? notes,
  }) {
    return WritingSession(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      wordsWritten: wordsWritten ?? this.wordsWritten,
      wordsDeleted: wordsDeleted ?? this.wordsDeleted,
      chapterId: chapterId ?? this.chapterId,
      type: type ?? this.type,
      notes: notes ?? this.notes,
    );
  }

  /// 计算写作速度（字/分钟）
  double get writingSpeed {
    if (duration == 0) return 0.0;
    return wordsWritten / duration;
  }

  /// 计算净增字数
  int get netWordsAdded => wordsWritten - wordsDeleted;

  @override
  String toString() => 'WritingSession(id: $id, duration: $duration, words: $wordsWritten)';
}

/// 会话类型枚举
enum SessionType {
  /// 新写作
  writing,
  /// 编辑修改
  editing,
  /// 校对
  proofreading,
  /// 规划
  planning,
  /// 研究
  research,
}

/// 统计汇总
@JsonSerializable()
class StatisticsSummary {
  /// 总字数
  final int totalWords;
  
  /// 总写作时长（分钟）
  final int totalDuration;
  
  /// 总章节数
  final int totalChapters;
  
  /// 平均每日字数
  final double averageDailyWords;
  
  /// 平均写作速度（字/分钟）
  final double averageWritingSpeed;
  
  /// 连续写作天数
  final int consecutiveDays;
  
  /// 目标达成天数
  final int goalAchievedDays;
  
  /// 总写作天数
  final int totalWritingDays;
  
  /// 最高单日字数
  final int maxDailyWords;
  
  /// 最长写作时长（分钟）
  final int maxDailyDuration;
  
  /// 统计时间范围
  final DateRange dateRange;

  const StatisticsSummary({
    required this.totalWords,
    required this.totalDuration,
    required this.totalChapters,
    required this.averageDailyWords,
    required this.averageWritingSpeed,
    required this.consecutiveDays,
    required this.goalAchievedDays,
    required this.totalWritingDays,
    required this.maxDailyWords,
    required this.maxDailyDuration,
    required this.dateRange,
  });

  /// 从JSON创建StatisticsSummary实例
  factory StatisticsSummary.fromJson(Map<String, dynamic> json) => 
      _$StatisticsSummaryFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$StatisticsSummaryToJson(this);

  /// 计算目标达成率
  double get goalAchievementRate {
    if (totalWritingDays == 0) return 0.0;
    return (goalAchievedDays / totalWritingDays * 100).clamp(0.0, 100.0);
  }

  @override
  String toString() => 'StatisticsSummary(totalWords: $totalWords, totalDuration: $totalDuration)';
}

/// 日期范围
@JsonSerializable()
class DateRange {
  /// 开始日期
  final DateTime start;
  
  /// 结束日期
  final DateTime end;

  const DateRange({
    required this.start,
    required this.end,
  });

  /// 从JSON创建DateRange实例
  factory DateRange.fromJson(Map<String, dynamic> json) => 
      _$DateRangeFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DateRangeToJson(this);

  /// 获取日期范围内的天数
  int get days => end.difference(start).inDays + 1;

  /// 检查日期是否在范围内
  bool contains(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final startOnly = DateTime(start.year, start.month, start.day);
    final endOnly = DateTime(end.year, end.month, end.day);
    
    return dateOnly.isAtSameMomentAs(startOnly) ||
           dateOnly.isAtSameMomentAs(endOnly) ||
           (dateOnly.isAfter(startOnly) && dateOnly.isBefore(endOnly));
  }

  @override
  String toString() => '${start.toString().split(' ')[0]} - ${end.toString().split(' ')[0]}';
}
