/// 图表数据实体
class ChartData {
  final DateTime date;
  final double wordCount;
  final Duration writingTime;

  const ChartData({
    required this.date,
    required this.wordCount,
    required this.writingTime,
  });

  /// 获取写作效率（字/分钟）
  double get efficiency {
    if (writingTime.inMinutes == 0) return 0.0;
    return wordCount / writingTime.inMinutes;
  }

  @override
  String toString() {
    return 'ChartData(date: $date, wordCount: $wordCount, writingTime: $writingTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartData &&
        other.date == date &&
        other.wordCount == wordCount &&
        other.writingTime == writingTime;
  }

  @override
  int get hashCode {
    return Object.hash(date, wordCount, writingTime);
  }
}
