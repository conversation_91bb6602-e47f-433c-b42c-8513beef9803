/// 写作统计实体
class WritingStats {
  final DateTime date;
  final int wordCount;
  final Duration writingTime;
  final int sessionsCount;
  final int charactersCount;

  const WritingStats({
    required this.date,
    required this.wordCount,
    required this.writingTime,
    required this.sessionsCount,
    required this.charactersCount,
  });

  /// 创建空的统计数据
  factory WritingStats.empty() {
    return WritingStats(
      date: DateTime.now(),
      wordCount: 0,
      writingTime: Duration.zero,
      sessionsCount: 0,
      charactersCount: 0,
    );
  }

  /// 复制并修改部分字段
  WritingStats copyWith({
    DateTime? date,
    int? wordCount,
    Duration? writingTime,
    int? sessionsCount,
    int? charactersCount,
  }) {
    return WritingStats(
      date: date ?? this.date,
      wordCount: wordCount ?? this.wordCount,
      writingTime: writingTime ?? this.writingTime,
      sessionsCount: sessionsCount ?? this.sessionsCount,
      charactersCount: charactersCount ?? this.charactersCount,
    );
  }

  /// 获取写作效率（字/分钟）
  double get efficiency {
    if (writingTime.inMinutes == 0) return 0.0;
    return wordCount / writingTime.inMinutes;
  }

  /// 获取平均每次写作字数
  double get averageWordsPerSession {
    if (sessionsCount == 0) return 0.0;
    return wordCount / sessionsCount;
  }

  /// 获取平均每次写作时长
  Duration get averageTimePerSession {
    if (sessionsCount == 0) return Duration.zero;
    return Duration(milliseconds: writingTime.inMilliseconds ~/ sessionsCount);
  }

  @override
  String toString() {
    return 'WritingStats(date: $date, wordCount: $wordCount, writingTime: $writingTime, sessionsCount: $sessionsCount, charactersCount: $charactersCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WritingStats &&
        other.date == date &&
        other.wordCount == wordCount &&
        other.writingTime == writingTime &&
        other.sessionsCount == sessionsCount &&
        other.charactersCount == charactersCount;
  }

  @override
  int get hashCode {
    return Object.hash(
      date,
      wordCount,
      writingTime,
      sessionsCount,
      charactersCount,
    );
  }
}
