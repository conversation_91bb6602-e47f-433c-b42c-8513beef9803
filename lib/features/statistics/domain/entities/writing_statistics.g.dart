// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'writing_statistics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WritingStatistics _$WritingStatisticsFromJson(Map<String, dynamic> json) =>
    WritingStatistics(
      date: DateTime.parse(json['date'] as String),
      novelId: json['novelId'] as String?,
      wordsWritten: (json['wordsWritten'] as num).toInt(),
      writingDuration: (json['writingDuration'] as num).toInt(),
      chaptersCreated: (json['chaptersCreated'] as num).toInt(),
      chaptersModified: (json['chaptersModified'] as num).toInt(),
      wordsDeleted: (json['wordsDeleted'] as num).toInt(),
      netWordsAdded: (json['netWordsAdded'] as num).toInt(),
      sessions: (json['sessions'] as List<dynamic>)
          .map((e) => WritingSession.fromJson(e as Map<String, dynamic>))
          .toList(),
      goalAchieved: json['goalAchieved'] as bool,
      dailyGoal: (json['dailyGoal'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$WritingStatisticsToJson(WritingStatistics instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'novelId': instance.novelId,
      'wordsWritten': instance.wordsWritten,
      'writingDuration': instance.writingDuration,
      'chaptersCreated': instance.chaptersCreated,
      'chaptersModified': instance.chaptersModified,
      'wordsDeleted': instance.wordsDeleted,
      'netWordsAdded': instance.netWordsAdded,
      'sessions': instance.sessions,
      'goalAchieved': instance.goalAchieved,
      'dailyGoal': instance.dailyGoal,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

WritingSession _$WritingSessionFromJson(Map<String, dynamic> json) =>
    WritingSession(
      id: json['id'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      duration: (json['duration'] as num).toInt(),
      wordsWritten: (json['wordsWritten'] as num).toInt(),
      wordsDeleted: (json['wordsDeleted'] as num).toInt(),
      chapterId: json['chapterId'] as String?,
      type: $enumDecode(_$SessionTypeEnumMap, json['type']),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$WritingSessionToJson(WritingSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'duration': instance.duration,
      'wordsWritten': instance.wordsWritten,
      'wordsDeleted': instance.wordsDeleted,
      'chapterId': instance.chapterId,
      'type': _$SessionTypeEnumMap[instance.type]!,
      'notes': instance.notes,
    };

const _$SessionTypeEnumMap = {
  SessionType.writing: 'writing',
  SessionType.editing: 'editing',
  SessionType.proofreading: 'proofreading',
  SessionType.planning: 'planning',
  SessionType.research: 'research',
};

StatisticsSummary _$StatisticsSummaryFromJson(Map<String, dynamic> json) =>
    StatisticsSummary(
      totalWords: (json['totalWords'] as num).toInt(),
      totalDuration: (json['totalDuration'] as num).toInt(),
      totalChapters: (json['totalChapters'] as num).toInt(),
      averageDailyWords: (json['averageDailyWords'] as num).toDouble(),
      averageWritingSpeed: (json['averageWritingSpeed'] as num).toDouble(),
      consecutiveDays: (json['consecutiveDays'] as num).toInt(),
      goalAchievedDays: (json['goalAchievedDays'] as num).toInt(),
      totalWritingDays: (json['totalWritingDays'] as num).toInt(),
      maxDailyWords: (json['maxDailyWords'] as num).toInt(),
      maxDailyDuration: (json['maxDailyDuration'] as num).toInt(),
      dateRange: DateRange.fromJson(json['dateRange'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$StatisticsSummaryToJson(StatisticsSummary instance) =>
    <String, dynamic>{
      'totalWords': instance.totalWords,
      'totalDuration': instance.totalDuration,
      'totalChapters': instance.totalChapters,
      'averageDailyWords': instance.averageDailyWords,
      'averageWritingSpeed': instance.averageWritingSpeed,
      'consecutiveDays': instance.consecutiveDays,
      'goalAchievedDays': instance.goalAchievedDays,
      'totalWritingDays': instance.totalWritingDays,
      'maxDailyWords': instance.maxDailyWords,
      'maxDailyDuration': instance.maxDailyDuration,
      'dateRange': instance.dateRange,
    };

DateRange _$DateRangeFromJson(Map<String, dynamic> json) => DateRange(
  start: DateTime.parse(json['start'] as String),
  end: DateTime.parse(json['end'] as String),
);

Map<String, dynamic> _$DateRangeToJson(DateRange instance) => <String, dynamic>{
  'start': instance.start.toIso8601String(),
  'end': instance.end.toIso8601String(),
};
