import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/responsive_layout.dart';

import '../providers/statistics_provider.dart';
import '../widgets/writing_stats_card.dart';
import '../widgets/progress_chart.dart';
import '../widgets/goal_tracker.dart';

/// 统计页面
class StatisticsPage extends StatefulWidget {
  const StatisticsPage({super.key});

  @override
  State<StatisticsPage> createState() => _StatisticsPageState();
}

class _StatisticsPageState extends State<StatisticsPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StatisticsProvider>().loadStatistics();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('写作统计'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<StatisticsProvider>().loadStatistics();
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showGoalSettings,
          ),
        ],
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Consumer<StatisticsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadStatistics(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 今日统计
                Text('今日统计', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 16),
                WritingStatsCard(title: '今日写作', stats: provider.todayStats),
                const SizedBox(height: 24),

                // 本周统计
                Text('本周统计', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 16),
                WritingStatsCard(title: '本周写作', stats: provider.weekStats),
                const SizedBox(height: 24),

                // 本月统计
                Text('本月统计', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 16),
                WritingStatsCard(title: '本月写作', stats: provider.monthStats),
                const SizedBox(height: 24),

                // 目标追踪
                Text('目标追踪', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 16),
                GoalTracker(
                  dailyGoal: provider.dailyGoal,
                  weeklyGoal: provider.weeklyGoal,
                  monthlyGoal: provider.monthlyGoal,
                  todayProgress: provider.todayStats.wordCount,
                  weekProgress: provider.weekStats.wordCount,
                  monthProgress: provider.monthStats.wordCount,
                ),
                const SizedBox(height: 24),

                // 进度图表
                Text('写作趋势', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 16),
                ProgressChart(data: provider.chartData),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTabletLayout() {
    return Consumer<StatisticsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadStatistics(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 统计卡片行
                Row(
                  children: [
                    Expanded(
                      child: WritingStatsCard(
                        title: '今日写作',
                        stats: provider.todayStats,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: WritingStatsCard(
                        title: '本周写作',
                        stats: provider.weekStats,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: WritingStatsCard(
                        title: '本月写作',
                        stats: provider.monthStats,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // 目标追踪和图表
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '目标追踪',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          GoalTracker(
                            dailyGoal: provider.dailyGoal,
                            weeklyGoal: provider.weeklyGoal,
                            monthlyGoal: provider.monthlyGoal,
                            todayProgress: provider.todayStats.wordCount,
                            weekProgress: provider.weekStats.wordCount,
                            monthProgress: provider.monthStats.wordCount,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 32),
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '写作趋势',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          ProgressChart(data: provider.chartData),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDesktopLayout() {
    return Consumer<StatisticsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return RefreshIndicator(
          onRefresh: () => provider.loadStatistics(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 统计卡片行
                Row(
                  children: [
                    Expanded(
                      child: WritingStatsCard(
                        title: '今日写作',
                        stats: provider.todayStats,
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: WritingStatsCard(
                        title: '本周写作',
                        stats: provider.weekStats,
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: WritingStatsCard(
                        title: '本月写作',
                        stats: provider.monthStats,
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: WritingStatsCard(
                        title: '总计',
                        stats: provider.totalStats,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 40),

                // 目标追踪和图表
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '目标追踪',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          GoalTracker(
                            dailyGoal: provider.dailyGoal,
                            weeklyGoal: provider.weeklyGoal,
                            monthlyGoal: provider.monthlyGoal,
                            todayProgress: provider.todayStats.wordCount,
                            weekProgress: provider.weekStats.wordCount,
                            monthProgress: provider.monthStats.wordCount,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 40),
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '写作趋势',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          ProgressChart(data: provider.chartData),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showGoalSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('设置写作目标'),
        content: Consumer<StatisticsProvider>(
          builder: (context, provider, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: '每日字数目标',
                    suffixText: '字',
                  ),
                  keyboardType: TextInputType.number,
                  controller: TextEditingController(
                    text: provider.dailyGoal.toString(),
                  ),
                  onChanged: (value) {
                    final goal = int.tryParse(value) ?? 0;
                    provider.updateDailyGoal(goal);
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: '每周字数目标',
                    suffixText: '字',
                  ),
                  keyboardType: TextInputType.number,
                  controller: TextEditingController(
                    text: provider.weeklyGoal.toString(),
                  ),
                  onChanged: (value) {
                    final goal = int.tryParse(value) ?? 0;
                    provider.updateWeeklyGoal(goal);
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: '每月字数目标',
                    suffixText: '字',
                  ),
                  keyboardType: TextInputType.number,
                  controller: TextEditingController(
                    text: provider.monthlyGoal.toString(),
                  ),
                  onChanged: (value) {
                    final goal = int.tryParse(value) ?? 0;
                    provider.updateMonthlyGoal(goal);
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
