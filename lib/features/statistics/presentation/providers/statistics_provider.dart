import 'package:flutter/foundation.dart';
import '../../domain/entities/writing_stats.dart';
import '../../domain/entities/chart_data.dart';

/// 统计Provider
class StatisticsProvider extends ChangeNotifier {
  bool _isLoading = false;
  
  // 统计数据
  WritingStats _todayStats = WritingStats.empty();
  WritingStats _weekStats = WritingStats.empty();
  WritingStats _monthStats = WritingStats.empty();
  WritingStats _totalStats = WritingStats.empty();
  
  // 目标设置
  int _dailyGoal = 1000;
  int _weeklyGoal = 7000;
  int _monthlyGoal = 30000;
  
  // 图表数据
  List<ChartData> _chartData = [];

  // Getters
  bool get isLoading => _isLoading;
  WritingStats get todayStats => _todayStats;
  WritingStats get weekStats => _weekStats;
  WritingStats get monthStats => _monthStats;
  WritingStats get totalStats => _totalStats;
  int get dailyGoal => _dailyGoal;
  int get weeklyGoal => _weeklyGoal;
  int get monthlyGoal => _monthlyGoal;
  List<ChartData> get chartData => _chartData;

  /// 加载统计数据
  Future<void> loadStatistics() async {
    _isLoading = true;
    notifyListeners();

    try {
      // TODO: 从数据库加载真实数据
      await _loadMockData();
    } catch (e) {
      debugPrint('加载统计数据失败: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 更新每日目标
  void updateDailyGoal(int goal) {
    _dailyGoal = goal;
    notifyListeners();
    // TODO: 保存到本地存储
  }

  /// 更新每周目标
  void updateWeeklyGoal(int goal) {
    _weeklyGoal = goal;
    notifyListeners();
    // TODO: 保存到本地存储
  }

  /// 更新每月目标
  void updateMonthlyGoal(int goal) {
    _monthlyGoal = goal;
    notifyListeners();
    // TODO: 保存到本地存储
  }

  /// 记录写作活动
  Future<void> recordWritingActivity({
    required int wordCount,
    required Duration writingTime,
    required String novelId,
  }) async {
    try {
      // TODO: 保存到数据库
      // 更新今日统计
      _todayStats = _todayStats.copyWith(
        wordCount: _todayStats.wordCount + wordCount,
        writingTime: Duration(
          milliseconds: _todayStats.writingTime.inMilliseconds + writingTime.inMilliseconds,
        ),
        sessionsCount: _todayStats.sessionsCount + 1,
      );
      
      notifyListeners();
    } catch (e) {
      debugPrint('记录写作活动失败: $e');
    }
  }

  /// 加载模拟数据
  Future<void> _loadMockData() async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    final now = DateTime.now();
    
    _todayStats = WritingStats(
      date: now,
      wordCount: 850,
      writingTime: const Duration(hours: 2, minutes: 15),
      sessionsCount: 3,
      charactersCount: 850 * 2,
    );

    _weekStats = WritingStats(
      date: now.subtract(const Duration(days: 7)),
      wordCount: 5200,
      writingTime: const Duration(hours: 12, minutes: 30),
      sessionsCount: 18,
      charactersCount: 5200 * 2,
    );

    _monthStats = WritingStats(
      date: now.subtract(const Duration(days: 30)),
      wordCount: 22500,
      writingTime: const Duration(hours: 45, minutes: 20),
      sessionsCount: 65,
      charactersCount: 22500 * 2,
    );

    _totalStats = WritingStats(
      date: DateTime(2024, 1, 1),
      wordCount: 125000,
      writingTime: const Duration(hours: 200, minutes: 45),
      sessionsCount: 320,
      charactersCount: 125000 * 2,
    );

    // 生成图表数据（最近30天）
    _chartData = List.generate(30, (index) {
      final date = now.subtract(Duration(days: 29 - index));
      final wordCount = (500 + (index * 50) + (index % 7 * 200)).toDouble();
      return ChartData(
        date: date,
        wordCount: wordCount,
        writingTime: Duration(minutes: (wordCount / 10).round()),
      );
    });
  }

  /// 获取写作效率（字/分钟）
  double getWritingEfficiency(WritingStats stats) {
    if (stats.writingTime.inMinutes == 0) return 0.0;
    return stats.wordCount / stats.writingTime.inMinutes;
  }

  /// 获取目标完成百分比
  double getDailyProgress() {
    if (_dailyGoal == 0) return 0.0;
    return (_todayStats.wordCount / _dailyGoal).clamp(0.0, 1.0);
  }

  double getWeeklyProgress() {
    if (_weeklyGoal == 0) return 0.0;
    return (_weekStats.wordCount / _weeklyGoal).clamp(0.0, 1.0);
  }

  double getMonthlyProgress() {
    if (_monthlyGoal == 0) return 0.0;
    return (_monthStats.wordCount / _monthlyGoal).clamp(0.0, 1.0);
  }

  /// 获取连续写作天数
  int getWritingStreak() {
    // TODO: 从数据库计算连续写作天数
    return 7; // 模拟数据
  }

  /// 获取平均每日字数
  double getAverageWordsPerDay() {
    // TODO: 从数据库计算
    return 750.0; // 模拟数据
  }

  /// 获取最高单日字数
  int getMaxWordsPerDay() {
    // TODO: 从数据库查询
    return 2500; // 模拟数据
  }

  /// 获取本月剩余天数需要的平均字数
  int getRequiredDailyWords() {
    final now = DateTime.now();
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    final remainingDays = daysInMonth - now.day + 1;
    
    if (remainingDays <= 0) return 0;
    
    final remainingWords = _monthlyGoal - _monthStats.wordCount;
    if (remainingWords <= 0) return 0;
    
    return (remainingWords / remainingDays).ceil();
  }
}
