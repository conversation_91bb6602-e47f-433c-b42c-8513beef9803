import 'package:flutter/material.dart';
import '../../domain/entities/chart_data.dart';

/// 进度图表组件
class ProgressChart extends StatelessWidget {
  final List<ChartData> data;

  const ProgressChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '写作趋势',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            if (data.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('暂无数据'),
                ),
              )
            else
              SizedBox(
                height: 200,
                child: CustomPaint(
                  painter: ChartPainter(
                    data: data,
                    primaryColor: Theme.of(context).colorScheme.primary,
                    backgroundColor: Theme.of(
                      context,
                    ).colorScheme.surfaceContainerHighest,
                  ),
                  child: Container(),
                ),
              ),

            const SizedBox(height: 16),

            // 图例
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildLegendItem(
                  context,
                  color: Theme.of(context).colorScheme.primary,
                  label: '每日字数',
                ),
                _buildLegendItem(
                  context,
                  color: Theme.of(context).colorScheme.secondary,
                  label: '平均线',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(
    BuildContext context, {
    required Color color,
    required String label,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }
}

/// 图表绘制器
class ChartPainter extends CustomPainter {
  final List<ChartData> data;
  final Color primaryColor;
  final Color backgroundColor;

  ChartPainter({
    required this.data,
    required this.primaryColor,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = primaryColor.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final gridPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = 1;

    // 计算数据范围
    final maxWordCount = data
        .map((d) => d.wordCount)
        .reduce((a, b) => a > b ? a : b);
    final minWordCount = data
        .map((d) => d.wordCount)
        .reduce((a, b) => a < b ? a : b);
    final range = maxWordCount - minWordCount;

    // 绘制网格线
    _drawGrid(canvas, size, gridPaint);

    // 绘制数据线
    final path = Path();
    final fillPath = Path();

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final y =
          size.height -
          ((data[i].wordCount - minWordCount) / range) * size.height;

      if (i == 0) {
        path.moveTo(x, y);
        fillPath.moveTo(x, size.height);
        fillPath.lineTo(x, y);
      } else {
        path.lineTo(x, y);
        fillPath.lineTo(x, y);
      }
    }

    // 完成填充路径
    fillPath.lineTo(size.width, size.height);
    fillPath.close();

    // 绘制填充区域
    canvas.drawPath(fillPath, fillPaint);

    // 绘制线条
    canvas.drawPath(path, paint);

    // 绘制数据点
    final pointPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final y =
          size.height -
          ((data[i].wordCount - minWordCount) / range) * size.height;
      canvas.drawCircle(Offset(x, y), 3, pointPaint);
    }

    // 绘制平均线
    final average =
        data.map((d) => d.wordCount).reduce((a, b) => a + b) / data.length;
    final avgY = size.height - ((average - minWordCount) / range) * size.height;
    final avgPaint = Paint()
      ..color = primaryColor.withOpacity(0.5)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    canvas.drawLine(Offset(0, avgY), Offset(size.width, avgY), avgPaint);
  }

  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // 绘制水平网格线
    for (int i = 0; i <= 4; i++) {
      final y = (i / 4) * size.height;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // 绘制垂直网格线
    for (int i = 0; i <= 6; i++) {
      final x = (i / 6) * size.width;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}
