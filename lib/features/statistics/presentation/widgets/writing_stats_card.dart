import 'package:flutter/material.dart';
import '../../domain/entities/writing_stats.dart';

/// 写作统计卡片
class WritingStatsCard extends StatelessWidget {
  final String title;
  final WritingStats stats;

  const WritingStatsCard({super.key, required this.title, required this.stats});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 字数统计
            _buildStatItem(
              context,
              icon: Icons.text_fields,
              label: '字数',
              value: _formatNumber(stats.wordCount),
              color: Colors.blue,
            ),
            const SizedBox(height: 12),

            // 写作时长
            _buildStatItem(
              context,
              icon: Icons.access_time,
              label: '时长',
              value: _formatDuration(stats.writingTime),
              color: Colors.green,
            ),
            const SizedBox(height: 12),

            // 写作次数
            _buildStatItem(
              context,
              icon: Icons.edit,
              label: '次数',
              value: '${stats.sessionsCount}次',
              color: Colors.orange,
            ),
            const SizedBox(height: 12),

            // 写作效率
            _buildStatItem(
              context,
              icon: Icons.speed,
              label: '效率',
              value: '${stats.efficiency.toStringAsFixed(1)}字/分',
              color: Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 16, color: color),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              Text(
                value,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatNumber(int number) {
    if (number >= 10000) {
      return '${(number / 10000).toStringAsFixed(1)}万';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}k';
    }
    return number.toString();
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '$hours小时$minutes分钟';
    } else {
      return '$minutes分钟';
    }
  }
}
