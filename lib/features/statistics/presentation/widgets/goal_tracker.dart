import 'package:flutter/material.dart';

/// 目标追踪组件
class GoalTracker extends StatelessWidget {
  final int dailyGoal;
  final int weeklyGoal;
  final int monthlyGoal;
  final int todayProgress;
  final int weekProgress;
  final int monthProgress;

  const GoalTracker({
    super.key,
    required this.dailyGoal,
    required this.weeklyGoal,
    required this.monthlyGoal,
    required this.todayProgress,
    required this.weekProgress,
    required this.monthProgress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '目标追踪',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // 每日目标
            _buildGoalItem(
              context,
              title: '今日目标',
              current: todayProgress,
              target: dailyGoal,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),

            // 每周目标
            _buildGoalItem(
              context,
              title: '本周目标',
              current: weekProgress,
              target: weeklyGoal,
              color: Colors.green,
            ),
            const SizedBox(height: 16),

            // 每月目标
            _buildGoalItem(
              context,
              title: '本月目标',
              current: monthProgress,
              target: monthlyGoal,
              color: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalItem(
    BuildContext context, {
    required String title,
    required int current,
    required int target,
    required Color color,
  }) {
    final progress = target > 0 ? (current / target).clamp(0.0, 1.0) : 0.0;
    final percentage = (progress * 100).toInt();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '$percentage%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${_formatNumber(current)}字',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            Text(
              '目标: ${_formatNumber(target)}字',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatNumber(int number) {
    if (number >= 10000) {
      return '${(number / 10000).toStringAsFixed(1)}万';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}k';
    }
    return number.toString();
  }
}
