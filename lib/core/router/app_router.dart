import 'package:go_router/go_router.dart';
import '../../features/main/presentation/pages/main_layout.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/novels/presentation/pages/novels_page.dart';
import '../../features/wiki/presentation/pages/wiki_page.dart';
import '../../features/wiki/presentation/pages/characters_page.dart';
import '../../features/materials/presentation/pages/materials_page.dart';
import '../../features/statistics/presentation/pages/statistics_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';

/// 应用路由配置
class AppRouter {
  static const String home = '/';
  static const String novels = '/novels';
  static const String novelDetail = '/novels/:id';
  static const String chapterEditor = '/novels/:novelId/chapters/:chapterId';
  static const String wiki = '/wiki';
  static const String characterDetail = '/wiki/characters/:id';
  static const String materials = '/materials';
  static const String settings = '/settings';

  static final GoRouter router = GoRouter(
    initialLocation: home,
    routes: [
      ShellRoute(
        builder: (context, state, child) =>
            MainLayout(location: state.uri.path, child: child),
        routes: [
          GoRoute(
            path: home,
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: novels,
            name: 'novels',
            builder: (context, state) => const NovelsPage(),
          ),
          GoRoute(
            path: wiki,
            name: 'wiki',
            builder: (context, state) => const WikiPage(),
            routes: [
              GoRoute(
                path: '/characters',
                name: 'characters',
                builder: (context, state) => const CharactersPage(),
              ),
            ],
          ),
          GoRoute(
            path: materials,
            name: 'materials',
            builder: (context, state) => const MaterialsPage(),
          ),
          GoRoute(
            path: '/statistics',
            name: 'statistics',
            builder: (context, state) => const StatisticsPage(),
          ),
          GoRoute(
            path: settings,
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
    ],
  );
}
