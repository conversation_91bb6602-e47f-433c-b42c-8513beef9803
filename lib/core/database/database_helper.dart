import 'dart:async';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../constants/app_constants.dart';

/// 数据库帮助类
class DatabaseHelper {
  static DatabaseHelper? _instance;
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() {
    _instance ??= DatabaseHelper._internal();
    return _instance!;
  }

  /// 获取数据库实例
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// 初始化数据库
  Future<Database> _initDatabase() async {
    // 为Web平台初始化数据库工厂
    if (kIsWeb) {
      databaseFactory = databaseFactoryFfiWeb;
    }

    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// 创建数据库表
  Future<void> _onCreate(Database db, int version) async {
    // 创建小说表
    await db.execute('''
      CREATE TABLE novels (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        author TEXT NOT NULL,
        cover_image_path TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        status TEXT NOT NULL,
        category TEXT NOT NULL,
        tags TEXT NOT NULL,
        word_count INTEGER NOT NULL DEFAULT 0,
        chapter_count INTEGER NOT NULL DEFAULT 0,
        is_completed INTEGER NOT NULL DEFAULT 0,
        last_read_chapter_id TEXT,
        daily_word_goal INTEGER,
        expected_completion_date INTEGER,
        notes TEXT
      )
    ''');

    // 创建章节表
    await db.execute('''
      CREATE TABLE chapters (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        order_index INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        status TEXT NOT NULL,
        word_count INTEGER NOT NULL DEFAULT 0,
        is_published INTEGER NOT NULL DEFAULT 0,
        summary TEXT,
        notes TEXT,
        writing_duration INTEGER NOT NULL DEFAULT 0,
        last_read_position INTEGER,
        FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
      )
    ''');

    // 创建角色表
    await db.execute('''
      CREATE TABLE characters (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        name TEXT NOT NULL,
        aliases TEXT NOT NULL,
        description TEXT NOT NULL,
        avatar_path TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        type TEXT NOT NULL,
        importance TEXT NOT NULL,
        basic_info TEXT NOT NULL,
        appearance TEXT NOT NULL,
        personality TEXT NOT NULL,
        backstory TEXT,
        relationships TEXT NOT NULL,
        skills TEXT NOT NULL,
        motivation TEXT,
        weakness TEXT,
        character_arc TEXT,
        notes TEXT,
        FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
      )
    ''');

    // 创建世界观设定表
    await db.execute('''
      CREATE TABLE world_settings (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        description TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        parent_id TEXT,
        geographic_info TEXT,
        historical_info TEXT,
        cultural_info TEXT,
        political_info TEXT,
        economic_info TEXT,
        technological_info TEXT,
        magical_info TEXT,
        image_paths TEXT NOT NULL,
        tags TEXT NOT NULL,
        notes TEXT,
        FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE,
        FOREIGN KEY (parent_id) REFERENCES world_settings (id) ON DELETE SET NULL
      )
    ''');

    // 创建时间线表
    await db.execute('''
      CREATE TABLE timelines (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        type TEXT NOT NULL,
        color INTEGER NOT NULL,
        is_visible INTEGER NOT NULL DEFAULT 1,
        notes TEXT,
        FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
      )
    ''');

    // 创建时间线事件表
    await db.execute('''
      CREATE TABLE timeline_events (
        id TEXT PRIMARY KEY,
        timeline_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        story_time TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        type TEXT NOT NULL,
        importance TEXT NOT NULL,
        related_character_ids TEXT NOT NULL,
        related_location_ids TEXT NOT NULL,
        related_chapter_ids TEXT NOT NULL,
        outcome TEXT,
        notes TEXT,
        FOREIGN KEY (timeline_id) REFERENCES timelines (id) ON DELETE CASCADE
      )
    ''');

    // 创建素材表
    await db.execute('''
      CREATE TABLE materials (
        id TEXT PRIMARY KEY,
        novel_id TEXT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        tags TEXT NOT NULL,
        source TEXT,
        attachment_paths TEXT NOT NULL,
        is_favorite INTEGER NOT NULL DEFAULT 0,
        is_used INTEGER NOT NULL DEFAULT 0,
        used_in_chapters TEXT NOT NULL,
        priority TEXT NOT NULL,
        notes TEXT,
        FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
      )
    ''');

    // 创建写作统计表
    await db.execute('''
      CREATE TABLE writing_statistics (
        date TEXT NOT NULL,
        novel_id TEXT,
        words_written INTEGER NOT NULL DEFAULT 0,
        writing_duration INTEGER NOT NULL DEFAULT 0,
        chapters_created INTEGER NOT NULL DEFAULT 0,
        chapters_modified INTEGER NOT NULL DEFAULT 0,
        words_deleted INTEGER NOT NULL DEFAULT 0,
        net_words_added INTEGER NOT NULL DEFAULT 0,
        goal_achieved INTEGER NOT NULL DEFAULT 0,
        daily_goal INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        PRIMARY KEY (date, novel_id),
        FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
      )
    ''');

    // 创建写作会话表
    await db.execute('''
      CREATE TABLE writing_sessions (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        novel_id TEXT,
        start_time INTEGER NOT NULL,
        end_time INTEGER,
        duration INTEGER NOT NULL DEFAULT 0,
        words_written INTEGER NOT NULL DEFAULT 0,
        words_deleted INTEGER NOT NULL DEFAULT 0,
        chapter_id TEXT,
        type TEXT NOT NULL,
        notes TEXT,
        FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE,
        FOREIGN KEY (chapter_id) REFERENCES chapters (id) ON DELETE SET NULL
      )
    ''');

    // 创建应用设置表
    await db.execute('''
      CREATE TABLE app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // 创建索引
    await _createIndexes(db);
  }

  /// 创建索引
  Future<void> _createIndexes(Database db) async {
    // 章节索引
    await db.execute(
      'CREATE INDEX idx_chapters_novel_id ON chapters (novel_id)',
    );
    await db.execute(
      'CREATE INDEX idx_chapters_order ON chapters (novel_id, order_index)',
    );

    // 角色索引
    await db.execute(
      'CREATE INDEX idx_characters_novel_id ON characters (novel_id)',
    );

    // 世界观设定索引
    await db.execute(
      'CREATE INDEX idx_world_settings_novel_id ON world_settings (novel_id)',
    );
    await db.execute(
      'CREATE INDEX idx_world_settings_parent_id ON world_settings (parent_id)',
    );

    // 时间线索引
    await db.execute(
      'CREATE INDEX idx_timelines_novel_id ON timelines (novel_id)',
    );
    await db.execute(
      'CREATE INDEX idx_timeline_events_timeline_id ON timeline_events (timeline_id)',
    );

    // 素材索引
    await db.execute(
      'CREATE INDEX idx_materials_novel_id ON materials (novel_id)',
    );
    await db.execute('CREATE INDEX idx_materials_type ON materials (type)');
    await db.execute(
      'CREATE INDEX idx_materials_category ON materials (category)',
    );

    // 统计索引
    await db.execute(
      'CREATE INDEX idx_writing_statistics_date ON writing_statistics (date)',
    );
    await db.execute(
      'CREATE INDEX idx_writing_sessions_date ON writing_sessions (date)',
    );
    await db.execute(
      'CREATE INDEX idx_writing_sessions_novel_id ON writing_sessions (novel_id)',
    );
  }

  /// 数据库升级
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // 根据版本进行数据库升级
    if (oldVersion < 2) {
      // 版本2的升级逻辑
    }
  }

  /// 关闭数据库
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  /// 删除数据库
  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
