import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../features/settings/presentation/providers/settings_provider.dart';

/// 主题切换器组件
class ThemeSwitcher extends StatelessWidget {
  const ThemeSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return PopupMenuButton<ThemeMode>(
          icon: Icon(_getThemeIcon(settingsProvider.themeMode)),
          onSelected: (ThemeMode mode) {
            settingsProvider.updateThemeMode(mode);
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: ThemeMode.system,
              child: Row(
                children: [
                  const Icon(Icons.brightness_auto),
                  const SizedBox(width: 8),
                  const Text('跟随系统'),
                  if (settingsProvider.themeMode == ThemeMode.system)
                    const Spacer(),
                  if (settingsProvider.themeMode == ThemeMode.system)
                    const Icon(Icons.check, color: Colors.green),
                ],
              ),
            ),
            PopupMenuItem(
              value: ThemeMode.light,
              child: Row(
                children: [
                  const Icon(Icons.light_mode),
                  const SizedBox(width: 8),
                  const Text('浅色模式'),
                  if (settingsProvider.themeMode == ThemeMode.light)
                    const Spacer(),
                  if (settingsProvider.themeMode == ThemeMode.light)
                    const Icon(Icons.check, color: Colors.green),
                ],
              ),
            ),
            PopupMenuItem(
              value: ThemeMode.dark,
              child: Row(
                children: [
                  const Icon(Icons.dark_mode),
                  const SizedBox(width: 8),
                  const Text('深色模式'),
                  if (settingsProvider.themeMode == ThemeMode.dark)
                    const Spacer(),
                  if (settingsProvider.themeMode == ThemeMode.dark)
                    const Icon(Icons.check, color: Colors.green),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  IconData _getThemeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}

/// 简单的主题切换开关
class ThemeToggleSwitch extends StatelessWidget {
  const ThemeToggleSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        final isDark = settingsProvider.themeMode == ThemeMode.dark;

        return Switch(
          value: isDark,
          onChanged: (value) {
            settingsProvider.updateThemeMode(
              value ? ThemeMode.dark : ThemeMode.light,
            );
          },
        );
      },
    );
  }
}

/// 主题切换按钮
class ThemeToggleButton extends StatelessWidget {
  const ThemeToggleButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return IconButton(
          icon: Icon(_getThemeIcon(settingsProvider.themeMode)),
          onPressed: () {
            _toggleTheme(settingsProvider);
          },
          tooltip: _getThemeTooltip(settingsProvider.themeMode),
        );
      },
    );
  }

  IconData _getThemeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  String _getThemeTooltip(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return '切换到深色模式';
      case ThemeMode.dark:
        return '切换到跟随系统';
      case ThemeMode.system:
        return '切换到浅色模式';
    }
  }

  void _toggleTheme(SettingsProvider provider) {
    switch (provider.themeMode) {
      case ThemeMode.light:
        provider.updateThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        provider.updateThemeMode(ThemeMode.system);
        break;
      case ThemeMode.system:
        provider.updateThemeMode(ThemeMode.light);
        break;
    }
  }
}

/// 字体大小调节器
class FontSizeAdjuster extends StatelessWidget {
  const FontSizeAdjuster({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.text_decrease),
              onPressed: () {
                final newSize = (settingsProvider.fontSize - 1).clamp(
                  12.0,
                  24.0,
                );
                settingsProvider.updateFontSize(newSize);
              },
            ),
            Text(
              '${settingsProvider.fontSize.round()}',
              style: TextStyle(fontSize: settingsProvider.fontSize),
            ),
            IconButton(
              icon: const Icon(Icons.text_increase),
              onPressed: () {
                final newSize = (settingsProvider.fontSize + 1).clamp(
                  12.0,
                  24.0,
                );
                settingsProvider.updateFontSize(newSize);
              },
            ),
          ],
        );
      },
    );
  }
}

/// 字体大小滑块
class FontSizeSlider extends StatelessWidget {
  const FontSizeSlider({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '字体大小: ${settingsProvider.fontSize.round()}',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Slider(
              value: settingsProvider.fontSize,
              min: 12.0,
              max: 24.0,
              divisions: 12,
              label: settingsProvider.fontSize.round().toString(),
              onChanged: (value) {
                settingsProvider.updateFontSize(value);
              },
            ),
            Text('示例文本', style: TextStyle(fontSize: settingsProvider.fontSize)),
          ],
        );
      },
    );
  }
}

/// 主题预览卡片
class ThemePreviewCard extends StatelessWidget {
  final ThemeMode themeMode;
  final bool isSelected;
  final VoidCallback onTap;

  const ThemePreviewCard({
    super.key,
    required this.themeMode,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = _getThemeData(themeMode, context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : Colors.grey,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Theme(
          data: theme,
          child: Card(
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(_getThemeIcon(themeMode)),
                      const SizedBox(width: 8),
                      Text(
                        _getThemeName(themeMode),
                        style: theme.textTheme.titleMedium,
                      ),
                      const Spacer(),
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          color: theme.colorScheme.primary,
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(4),
                              bottomLeft: Radius.circular(4),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Container(color: theme.colorScheme.surface),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  ThemeData _getThemeData(ThemeMode mode, BuildContext context) {
    switch (mode) {
      case ThemeMode.light:
        return ThemeData.light(useMaterial3: true);
      case ThemeMode.dark:
        return ThemeData.dark(useMaterial3: true);
      case ThemeMode.system:
        return Theme.of(context);
    }
  }

  IconData _getThemeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  String _getThemeName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }
}
