import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// 响应式布局组件
class ResponsiveLayout extends StatelessWidget {
  /// 移动端布局
  final Widget mobile;
  
  /// 平板端布局（可选）
  final Widget? tablet;
  
  /// 桌面端布局（可选）
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    if (AppTheme.isDesktop(context)) {
      return desktop ?? tablet ?? mobile;
    } else if (AppTheme.isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
}

/// 响应式容器
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? maxWidth;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding ?? AppTheme.getResponsivePadding(context),
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? 
                (AppTheme.isDesktop(context) ? 1200 : double.infinity),
          ),
          child: child,
        ),
      ),
    );
  }
}

/// 响应式网格
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double spacing;
  final double runSpacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.spacing = 16,
    this.runSpacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    int columns;
    if (AppTheme.isDesktop(context)) {
      columns = desktopColumns ?? 3;
    } else if (AppTheme.isTablet(context)) {
      columns = tabletColumns ?? 2;
    } else {
      columns = mobileColumns ?? 1;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
        childAspectRatio: 1.0,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// 响应式侧边栏布局
class ResponsiveSidebarLayout extends StatelessWidget {
  final Widget sidebar;
  final Widget body;
  final double sidebarWidth;
  final bool showSidebarOnMobile;

  const ResponsiveSidebarLayout({
    super.key,
    required this.sidebar,
    required this.body,
    this.sidebarWidth = 280,
    this.showSidebarOnMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (AppTheme.isMobile(context) && !showSidebarOnMobile) {
      return body;
    }

    return Row(
      children: [
        if (AppTheme.isTablet(context) || AppTheme.isDesktop(context))
          SizedBox(
            width: sidebarWidth,
            child: sidebar,
          ),
        Expanded(child: body),
      ],
    );
  }
}

/// 响应式导航栏
class ResponsiveNavigation extends StatelessWidget {
  final List<NavigationItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;

  const ResponsiveNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (AppTheme.isMobile(context)) {
      return BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: currentIndex,
        onTap: onTap,
        items: items
            .map((item) => BottomNavigationBarItem(
                  icon: Icon(item.icon),
                  label: item.label,
                ))
            .toList(),
      );
    } else {
      return NavigationRail(
        selectedIndex: currentIndex,
        onDestinationSelected: onTap,
        labelType: NavigationRailLabelType.all,
        destinations: items
            .map((item) => NavigationRailDestination(
                  icon: Icon(item.icon),
                  label: Text(item.label),
                ))
            .toList(),
      );
    }
  }
}

/// 导航项
class NavigationItem {
  final IconData icon;
  final String label;
  final String? route;

  const NavigationItem({
    required this.icon,
    required this.label,
    this.route,
  });
}

/// 响应式对话框
class ResponsiveDialog extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;

  const ResponsiveDialog({
    super.key,
    required this.child,
    this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    if (AppTheme.isMobile(context)) {
      return Dialog.fullscreen(
        child: Scaffold(
          appBar: AppBar(
            title: title != null ? Text(title!) : null,
            actions: actions,
          ),
          body: child,
        ),
      );
    } else {
      return AlertDialog(
        title: title != null ? Text(title!) : null,
        content: SizedBox(
          width: AppTheme.isDesktop(context) ? 600 : 400,
          child: child,
        ),
        actions: actions,
      );
    }
  }

  /// 显示响应式对话框
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    List<Widget>? actions,
  }) {
    return showDialog<T>(
      context: context,
      builder: (context) => ResponsiveDialog(
        title: title,
        actions: actions,
        child: child,
      ),
    );
  }
}

/// 响应式卡片
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final VoidCallback? onTap;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardPadding = padding ?? 
        (AppTheme.isMobile(context) 
            ? const EdgeInsets.all(12) 
            : const EdgeInsets.all(16));

    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: cardPadding,
          child: child,
        ),
      ),
    );
  }
}

/// 响应式文本
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final baseStyle = style ?? theme.textTheme.bodyMedium;
    
    // 根据屏幕大小调整字体大小
    final scaleFactor = AppTheme.isMobile(context) ? 0.9 : 1.0;
    final responsiveStyle = baseStyle?.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * scaleFactor,
    );

    return Text(
      text,
      style: responsiveStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
