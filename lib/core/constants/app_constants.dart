/// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'NovelICE';
  static const String appVersion = '1.0.0';
  static const String appDescription = '小说集成创作环境';
  
  // 数据库
  static const String databaseName = 'novel_ice.db';
  static const int databaseVersion = 1;
  
  // 存储键
  static const String themeKey = 'theme_mode';
  static const String fontSizeKey = 'font_size';
  static const String lastOpenedNovelKey = 'last_opened_novel';
  
  // 默认值
  static const double defaultFontSize = 16.0;
  static const double minFontSize = 12.0;
  static const double maxFontSize = 24.0;
  
  // 分页
  static const int defaultPageSize = 20;
  
  // 文件路径
  static const String novelsDirectory = 'novels';
  static const String materialsDirectory = 'materials';
  static const String backupDirectory = 'backup';
}
