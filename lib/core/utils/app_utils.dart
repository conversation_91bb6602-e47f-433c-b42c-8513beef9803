import 'package:intl/intl.dart';

/// 应用工具类
class AppUtils {
  /// 格式化日期时间
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }

  /// 格式化日期
  static String formatDate(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  /// 格式化时间
  static String formatTime(DateTime dateTime) {
    return DateFormat('HH:mm').format(dateTime);
  }

  /// 格式化相对时间
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 格式化字数
  static String formatWordCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万字';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}千字';
    } else {
      return '$count字';
    }
  }

  /// 计算阅读时间（按每分钟300字计算）
  static String calculateReadingTime(int wordCount) {
    final minutes = (wordCount / 300).ceil();
    if (minutes >= 60) {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? '$hours小时$remainingMinutes分钟' : '$hours小时';
    } else {
      return '$minutes分钟';
    }
  }

  /// 验证邮箱格式
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 生成随机颜色
  static int generateRandomColor() {
    return (0xFF000000 +
        (0xFFFFFF * (DateTime.now().millisecondsSinceEpoch % 1000) / 1000)
            .round());
  }
}
