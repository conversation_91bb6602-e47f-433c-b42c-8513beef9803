import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'core/database/database_helper.dart';
import 'features/novels/data/datasources/novel_dao.dart';
import 'features/novels/data/datasources/chapter_dao.dart';
import 'features/novels/data/repositories/novel_repository_impl.dart';
import 'features/novels/presentation/providers/novel_provider.dart';
import 'features/materials/data/datasources/material_dao.dart';
import 'features/materials/data/repositories/material_repository_impl.dart';
import 'features/materials/presentation/providers/material_provider.dart';
import 'features/settings/presentation/providers/settings_provider.dart';
import 'features/wiki/presentation/providers/character_provider.dart';
import 'features/statistics/presentation/providers/statistics_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化数据库 (暂时禁用以便在Web上演示)
  final databaseHelper = DatabaseHelper();
  // await databaseHelper.database;

  runApp(NovelICEApp(databaseHelper: databaseHelper));
}

class NovelICEApp extends StatelessWidget {
  final DatabaseHelper databaseHelper;

  const NovelICEApp({super.key, required this.databaseHelper});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // 设置Provider
        ChangeNotifierProvider(
          create: (context) => SettingsProvider()..loadSettings(),
        ),

        // 小说Provider
        ChangeNotifierProvider(
          create: (context) {
            final novelDao = NovelDao(databaseHelper);
            final chapterDao = ChapterDao(databaseHelper);
            final repository = NovelRepositoryImpl(
              novelDao: novelDao,
              chapterDao: chapterDao,
            );
            return NovelProvider(repository: repository);
          },
        ),

        // 素材Provider
        ChangeNotifierProvider(
          create: (context) {
            final materialDao = MaterialDao(databaseHelper);
            final repository = MaterialRepositoryImpl(materialDao: materialDao);
            return MaterialProvider(repository: repository);
          },
        ),

        // 角色Provider
        ChangeNotifierProvider(create: (context) => CharacterProvider()),

        // 统计Provider
        ChangeNotifierProvider(create: (context) => StatisticsProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return MaterialApp.router(
            title: 'NovelICE',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: settingsProvider.themeMode,
            routerConfig: AppRouter.router,
          );
        },
      ),
    );
  }
}
