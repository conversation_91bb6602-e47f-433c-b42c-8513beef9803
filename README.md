# NovelICE - 小说集成创作环境

NovelICE（Novel Integrated Creative Environment）是一个专为小说创作者设计的综合性写作应用程序。它提供了完整的小说创作工具链，包括小说管理、Wiki系统、素材库、统计分析和设置管理等功能。

## 🌟 主要特性

### 📚 小说管理
- **作品管理**: 创建、编辑、删除小说作品
- **章节管理**: 完整的章节结构管理
- **状态跟踪**: 草稿、构思中、写作中、暂停、已完成、已发布、已废弃
- **进度统计**: 字数统计、章节数量、完成进度

### 📖 Wiki系统
- **角色管理**: 详细的角色信息、关系网络
- **世界观设定**: 世界背景、设定资料
- **时间线**: 故事时间线管理
- **物品道具**: 重要物品和道具记录
- **地点场景**: 场景和地点描述
- **组织机构**: 团体和组织信息

### 📝 素材库
- **多类型素材**: 灵感、想法、情节片段、对话、场景描述等
- **分类管理**: 自定义分类和标签系统
- **搜索过滤**: 强大的搜索和过滤功能
- **收藏系统**: 重要素材收藏功能
- **使用追踪**: 素材在章节中的使用情况

### 📊 统计分析
- **写作统计**: 每日、每周、每月写作数据
- **目标管理**: 设定和追踪写作目标
- **进度图表**: 可视化写作进度
- **效率分析**: 写作效率和习惯分析

### ⚙️ 设置管理
- **主题切换**: 深色/浅色模式
- **字体设置**: 字体大小、字体家族、行间距
- **编辑器设置**: 自动保存、自动换行等
- **数据管理**: 数据备份和恢复

## 🏗️ 技术架构

### 前端框架
- **Flutter**: 跨平台UI框架
- **Material Design 3**: 现代化UI设计
- **响应式设计**: 支持桌面端和移动端

### 状态管理
- **Provider**: 状态管理解决方案
- **ChangeNotifier**: 响应式数据更新

### 数据存储
- **SQLite**: 本地数据库存储
- **SharedPreferences**: 应用设置存储
- **JSON序列化**: 数据序列化和反序列化

### 架构模式
- **Clean Architecture**: 清晰的分层架构
- **Repository Pattern**: 数据访问抽象
- **DAO Pattern**: 数据访问对象
## 📁 项目结构

```
lib/
├── core/                    # 核心功能
│   ├── constants/          # 常量定义
│   ├── database/           # 数据库配置
│   ├── router/             # 路由配置
│   ├── theme/              # 主题配置
│   ├── utils/              # 工具函数
│   └── widgets/            # 通用组件
├── features/               # 功能模块
│   ├── home/               # 首页模块
│   ├── novels/             # 小说管理模块
│   ├── wiki/               # Wiki系统模块
│   ├── materials/          # 素材库模块
│   ├── statistics/         # 统计模块
│   └── settings/           # 设置模块
└── main.dart               # 应用入口
```

每个功能模块采用以下结构：
```
feature/
├── data/                   # 数据层
│   ├── datasources/        # 数据源
│   ├── models/             # 数据模型
│   └── repositories/       # 仓库实现
├── domain/                 # 领域层
│   ├── entities/           # 实体类
│   ├── repositories/       # 仓库接口
│   └── usecases/           # 用例
└── presentation/           # 表现层
    ├── pages/              # 页面
    ├── widgets/            # 组件
    └── providers/          # 状态管理
```

## 🚀 快速开始

### 环境要求
- Flutter SDK >= 3.0.0
- Dart SDK >= 3.0.0

### 安装依赖
```bash
flutter pub get
```

### 生成代码
```bash
flutter packages pub run build_runner build
```

### 运行应用
```bash
# Web端
flutter run -d chrome

# 桌面端
flutter run -d macos    # macOS
flutter run -d windows  # Windows
flutter run -d linux    # Linux

# 移动端
flutter run -d ios      # iOS
flutter run -d android  # Android
```

### 运行测试
```bash
flutter test
```

## 📱 支持平台

- ✅ Web (Chrome, Firefox, Safari, Edge)
- ✅ macOS
- ✅ Windows
- ✅ Linux
- ✅ iOS
- ✅ Android

## 🎨 UI设计

### 设计原则
- **简洁性**: 界面简洁，功能明确
- **一致性**: 统一的设计语言和交互模式
- **可访问性**: 支持无障碍访问
- **响应式**: 适配不同屏幕尺寸

### 主题支持
- 🌞 浅色模式
- 🌙 深色模式
- 🔄 系统跟随

### 颜色方案
- **主色调**: Material Design 3 动态颜色
- **语义颜色**: 成功、警告、错误、信息
- **中性色**: 文本、背景、边框

## 🔧 开发指南

### 代码规范
- 遵循 Dart 官方代码规范
- 使用 `flutter analyze` 进行代码检查
- 使用 `dart format` 进行代码格式化

### 提交规范
- 使用语义化提交信息
- 格式：`type(scope): description`
- 类型：feat, fix, docs, style, refactor, test, chore

### 测试策略
- **单元测试**: 测试业务逻辑和实体类
- **组件测试**: 测试UI组件
- **集成测试**: 测试完整功能流程

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解贡献指南。

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 功能建议: [GitHub Discussions]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**NovelICE** - 让小说创作更加高效和愉悦！ ✨
