# NovelICE 开发文档

## 项目概述

NovelICE 是一个基于 Flutter 开发的小说集成创作环境，采用 Clean Architecture 架构模式，支持多平台部署。

## 开发环境设置

### 必需工具
- Flutter SDK 3.0.0+
- Dart SDK 3.0.0+
- IDE: VS Code 或 Android Studio
- Git

### 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd novel_ice_app

# 安装依赖
flutter pub get

# 生成代码
flutter packages pub run build_runner build

# 运行项目
flutter run -d chrome
```

## 架构说明

### Clean Architecture 分层

1. **Presentation Layer (表现层)**
   - Pages: UI页面
   - Widgets: UI组件
   - Providers: 状态管理

2. **Domain Layer (领域层)**
   - Entities: 业务实体
   - Repositories: 仓库接口
   - Use Cases: 业务用例

3. **Data Layer (数据层)**
   - Data Sources: 数据源
   - Models: 数据模型
   - Repositories: 仓库实现

### 状态管理

使用 Provider 模式进行状态管理：

```dart
// Provider 定义
class NovelProvider extends ChangeNotifier {
  List<Novel> _novels = [];
  
  List<Novel> get novels => _novels;
  
  Future<void> loadNovels() async {
    // 加载逻辑
    notifyListeners();
  }
}

// 在 Widget 中使用
Consumer<NovelProvider>(
  builder: (context, provider, child) {
    return ListView.builder(
      itemCount: provider.novels.length,
      itemBuilder: (context, index) {
        return NovelCard(novel: provider.novels[index]);
      },
    );
  },
)
```

### 数据存储

#### SQLite 数据库
- 使用 `sqflite` 包进行本地数据存储
- 数据库文件: `novel_ice.db`
- 主要表: novels, chapters, characters, materials

#### SharedPreferences
- 存储应用设置
- 用户偏好配置

### 路由管理

使用 `go_router` 进行路由管理：

```dart
final router = GoRouter(
  routes: [
    ShellRoute(
      builder: (context, state, child) => MainLayout(child: child),
      routes: [
        GoRoute(
          path: '/novels',
          builder: (context, state) => const NovelsPage(),
        ),
        // 其他路由...
      ],
    ),
  ],
);
```

## 功能模块

### 1. 小说管理模块 (novels)
- **实体**: Novel, Chapter
- **功能**: 创建、编辑、删除小说和章节
- **状态**: 草稿、构思中、写作中、暂停、已完成、已发布、已废弃

### 2. Wiki系统模块 (wiki)
- **实体**: Character, WorldSetting, Timeline, Item, Location, Organization
- **功能**: 管理小说相关的设定资料

### 3. 素材库模块 (materials)
- **实体**: Material
- **类型**: 灵感、想法、情节片段、对话、场景描述等
- **功能**: 分类管理、搜索过滤、收藏系统

### 4. 统计模块 (statistics)
- **实体**: WritingStats, ChartData
- **功能**: 写作统计、目标管理、进度图表

### 5. 设置模块 (settings)
- **实体**: AppSettings, EditorSettings, AutoSaveSettings
- **功能**: 主题切换、字体设置、编辑器配置

## 开发规范

### 代码风格
- 遵循 Dart 官方代码规范
- 使用 `dart format` 格式化代码
- 使用 `flutter analyze` 检查代码质量

### 命名规范
- 文件名: 小写字母 + 下划线 (snake_case)
- 类名: 大驼峰 (PascalCase)
- 变量名: 小驼峰 (camelCase)
- 常量: 大写字母 + 下划线 (SCREAMING_SNAKE_CASE)

### 文件组织
```
lib/features/feature_name/
├── data/
│   ├── datasources/
│   │   └── feature_dao.dart
│   ├── models/
│   │   └── feature_model.dart
│   └── repositories/
│       └── feature_repository_impl.dart
├── domain/
│   ├── entities/
│   │   └── feature.dart
│   └── repositories/
│       └── feature_repository.dart
└── presentation/
    ├── pages/
    │   └── feature_page.dart
    ├── widgets/
    │   └── feature_widget.dart
    └── providers/
        └── feature_provider.dart
```

### 注释规范
```dart
/// 小说实体类
/// 
/// 包含小说的基本信息，如标题、作者、状态等
class Novel {
  /// 唯一标识符
  final String id;
  
  /// 小说标题
  final String title;
  
  /// 创建小说实例
  /// 
  /// [id] 唯一标识符
  /// [title] 小说标题
  const Novel({
    required this.id,
    required this.title,
  });
}
```

## 测试策略

### 单元测试
- 测试业务逻辑
- 测试实体类
- 测试工具函数

### 组件测试
- 测试 Widget 渲染
- 测试用户交互
- 测试状态变化

### 集成测试
- 测试完整功能流程
- 测试数据持久化
- 测试跨模块交互

### 运行测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/features/novels/domain/entities/novel_test.dart

# 生成测试覆盖率报告
flutter test --coverage
```

## 构建和部署

### Web 构建
```bash
flutter build web --release
```

### 桌面构建
```bash
# macOS
flutter build macos --release

# Windows
flutter build windows --release

# Linux
flutter build linux --release
```

### 移动端构建
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

## 常见问题

### 1. 数据库初始化失败
- 检查 SQLite 依赖是否正确安装
- 确认数据库文件路径权限

### 2. 状态更新不及时
- 确保调用 `notifyListeners()`
- 检查 Provider 是否正确注册

### 3. 路由跳转失败
- 检查路由路径是否正确
- 确认路由参数格式

### 4. 主题切换不生效
- 检查 ThemeMode 设置
- 确认主题数据是否正确传递

## 性能优化

### 1. 列表优化
- 使用 `ListView.builder` 进行懒加载
- 实现虚拟滚动

### 2. 状态管理优化
- 避免不必要的 `notifyListeners()` 调用
- 使用 `Selector` 进行精确更新

### 3. 数据库优化
- 使用索引提高查询性能
- 实现分页加载

### 4. 内存管理
- 及时释放不需要的资源
- 使用 `dispose()` 清理状态

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request
5. 代码审查
6. 合并到主分支

### 提交信息格式
```
type(scope): description

[optional body]

[optional footer]
```

类型:
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建

## 联系方式

- 项目维护者: [维护者信息]
- 技术支持: [支持邮箱]
- 问题反馈: [GitHub Issues]
